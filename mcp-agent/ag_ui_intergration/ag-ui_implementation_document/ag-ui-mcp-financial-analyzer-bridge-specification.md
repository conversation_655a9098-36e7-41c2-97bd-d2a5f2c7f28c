# AG-UI to MCP Financial Analyzer Bridge/Middleware Specification

**Version:** 1.0.0
**Date:** 2025-07-08
**Author:** Augment Agent

## Executive Summary

This document provides a comprehensive technical specification for implementing a bridge/middleware that connects AG-UI events to the MCP Financial Analyzer service. The bridge enables seamless integration between AG-UI protocol-compliant frontends and the existing MCP Financial Analyzer backend, providing real-time streaming financial analysis capabilities.

## Table of Contents

1. [Analysis Findings](#analysis-findings)
2. [Architecture Design](#architecture-design)
3. [Event Mapping Specification](#event-mapping-specification)
4. [Implementation Plan](#implementation-plan)
5. [API Reference](#api-reference)
6. [Testing Strategy](#testing-strategy)
7. [Deployment Guide](#deployment-guide)
8. [Appendices](#appendices)

## Analysis Findings

### AG-UI Protocol Analysis

**Key Components Identified:**
- **AbstractAgent**: Base class for all AG-UI agents with event streaming capabilities
- **Event System**: Streaming architecture with lifecycle, text message, tool call, and state events
- **RunAgentInput Schema**: Standardized input format with threadId, messages, tools, and context
- **Observable Pattern**: RxJS-based streaming for real-time event delivery

**Core Event Types:**
- Lifecycle: `RUN_STARTED`, `RUN_FINISHED`, `RUN_ERROR`
- Text Messages: `TEXT_MESSAGE_START`, `TEXT_MESSAGE_CONTENT`, `TEXT_MESSAGE_END`
- Tool Calls: `TOOL_CALL_START`, `TOOL_CALL_ARGS`, `TOOL_CALL_END`
- State Management: `STATE_SNAPSHOT`, `STATE_DELTA`

### MCP Financial Analyzer Analysis

**Service Capabilities:**
- **Research Agent**: Google Search-based financial data collection with quality evaluation
- **Analyst Agent**: Financial data analysis and insight generation
- **Report Writer**: Professional markdown report generation
- **Orchestrator**: Workflow coordination with quality control loops

**Existing AG-UI Integration:**
- Partial AG-UI components already implemented in `ag_ui_sample/` directory
- Event streaming infrastructure with `FinancialAnalysisEventStream`
- State management with `AGUIStateManager`
- Tool bridging with `MCPToAGUIToolBridge`
- HTTP server with SSE support in `http_server.py`

**API Structure:**
- Main entry point: `main.py` with dual MCP/AG-UI mode support
- HTTP endpoint: `POST /analyze` accepting `RunAgentInput`
- Output: Server-Sent Events stream of AG-UI `BaseEvent` objects

## Architecture Design

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[AG-UI Frontend] --> B[HTTP Client]
    end

    subgraph "Bridge/Middleware Layer"
        B --> C[AG-UI Bridge Middleware]
        C --> D[Event Transformer]
        C --> E[Request Validator]
        C --> F[Response Mapper]
    end

    subgraph "MCP Financial Analyzer"
        D --> G[HTTP Server]
        G --> H[AG-UI Orchestrator]
        H --> I[Research Agent]
        H --> J[Analyst Agent]
        H --> K[Report Writer]
    end

    subgraph "Data Layer"
        I --> L[Google Search MCP]
        K --> M[File System MCP]
    end
```

### Component Architecture

**1. AG-UI Bridge Middleware**
- Extends `AbstractAgent` class
- Implements AG-UI protocol compliance
- Handles HTTP communication with MCP Financial Analyzer
- Manages event streaming and state synchronization

**2. Event Transformation Layer**
- Converts AG-UI events to MCP Financial Analyzer API calls
- Maps MCP responses back to AG-UI events
- Handles streaming data transformation
- Manages error translation

**3. State Management**
- Synchronizes AG-UI state with MCP workflow progress
- Tracks analysis phases and completion status
- Manages thread-specific state isolation
- Provides real-time progress updates

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant F as Frontend
    participant B as Bridge
    participant M as MCP Service

    F->>B: RunAgentInput
    B->>F: RUN_STARTED
    B->>M: POST /analyze
    M->>B: SSE Stream
    loop Event Processing
        B->>F: TEXT_MESSAGE_CONTENT
        B->>F: STATE_DELTA
        B->>F: TOOL_CALL_*
    end
    B->>F: RUN_FINISHED
```

## Event Mapping Specification

### Input Event Mapping

| AG-UI Input | MCP Financial Analyzer Mapping | Transformation |
|-------------|--------------------------------|----------------|
| `threadId` | HTTP request correlation ID | Direct mapping |
| `messages` | Company name extraction | Parse last message for company name |
| `tools` | Frontend tool definitions | Pass-through to MCP service |
| `context` | Analysis parameters | Map to MCP workflow configuration |
| `state` | Previous analysis state | Restore workflow state if available |

### Output Event Mapping

| MCP Event Source | AG-UI Event Type | Transformation Logic |
|------------------|------------------|---------------------|
| Workflow start | `RUN_STARTED` | Map thread/run IDs |
| Research phase | `TEXT_MESSAGE_*` + `STATE_DELTA` | Stream research progress |
| Analysis phase | `TEXT_MESSAGE_*` + `STATE_DELTA` | Stream analysis insights |
| Report generation | `TEXT_MESSAGE_*` + `STATE_DELTA` | Stream report creation |
| Tool execution | `TOOL_CALL_*` | Map MCP tool calls to AG-UI format |
| Workflow completion | `RUN_FINISHED` | Include final results |
| Error conditions | `RUN_ERROR` | Map error details and recovery options |

### State Schema Mapping

```typescript
// AG-UI State Schema
interface FinancialAnalysisState {
  company: string;
  analysisPhase: 'research' | 'analysis' | 'reporting' | 'complete';
  progress: number; // 0-100
  currentOperation: string;
  researchData?: any;
  analysisResults?: any;
  reportPath?: string;
  error?: string;
}

// MCP Workflow State Mapping
interface MCPWorkflowState {
  status: 'running' | 'success' | 'error';
  phases: {
    research: { status: string; results?: any };
    analysis: { status: string; results?: any };
    reporting: { status: string; results?: any };
  };
  metadata: {
    threadId: string;
    totalPhases: number;
    enhancedMode: boolean;
  };
}
```

## Implementation Plan

### Phase 1: Core Bridge Implementation (Week 1)

**Files to Create:**
- `mcp-agent/ag_ui_intergration/bridge/ag_ui_financial_bridge.py`
- `mcp-agent/ag_ui_intergration/bridge/event_transformer.py`
- `mcp-agent/ag_ui_intergration/bridge/state_synchronizer.py`

**Tasks:**
1. Implement `FinancialAnalyzerBridge` extending `AbstractAgent`
2. Create event transformation utilities
3. Implement HTTP client for MCP service communication
4. Add comprehensive error handling and logging

### Phase 2: Enhanced Integration (Week 2)

**Files to Enhance:**
- `mcp-agent/examples/usecases/mcp_financial_analyzer/http_server.py`
- `mcp-agent/examples/usecases/mcp_financial_analyzer/ag_ui_sample/`

**Tasks:**
1. Enhance HTTP server with improved AG-UI compliance
2. Add authentication and rate limiting
3. Implement advanced state management features
4. Add monitoring and metrics collection

### Phase 3: Testing and Validation (Week 3)

**Files to Create:**
- `mcp-agent/ag_ui_intergration/bridge/tests/`
- Integration test suites
- Performance benchmarks

**Tasks:**
1. Unit tests for all bridge components
2. Integration tests with real MCP service
3. Performance testing and optimization
4. Documentation and examples

### Directory Structure

```
mcp-agent/ag_ui_intergration/
├── bridge/
│   ├── __init__.py
│   ├── ag_ui_financial_bridge.py      # Main bridge implementation
│   ├── event_transformer.py           # Event transformation logic
│   ├── state_synchronizer.py          # State management
│   ├── http_client.py                 # MCP service client
│   ├── error_handler.py               # Error handling utilities
│   └── tests/
│       ├── test_bridge.py
│       ├── test_transformer.py
│       └── test_integration.py
├── examples/
│   ├── basic_usage.py
│   ├── advanced_configuration.py
│   └── frontend_integration.html
└── docs/
    ├── api_reference.md
    ├── configuration_guide.md
    └── troubleshooting.md
```

## API Reference

### Bridge Configuration

```python
from ag_ui_intergration.bridge import FinancialAnalyzerBridge

# Basic configuration
bridge = FinancialAnalyzerBridge(
    mcp_service_url="http://localhost:8080",
    timeout=300,  # 5 minutes
    max_retries=3
)

# Advanced configuration
bridge = FinancialAnalyzerBridge(
    mcp_service_url="http://localhost:8080",
    timeout=300,
    max_retries=3,
    auth_token="your-auth-token",
    cors_origins=["http://localhost:3000"],
    enable_metrics=True,
    log_level="INFO"
)
```

### Usage Examples

```python
# Basic usage
async def analyze_company(company_name: str):
    input_data = RunAgentInput(
        threadId="thread_123",
        runId="run_456",
        messages=[{
            "role": "user",
            "content": f"Analyze {company_name}"
        }],
        context=[{
            "description": "company_name",
            "value": company_name
        }]
    )

    # Stream events
    async for event in bridge.run(input_data):
        print(f"Event: {event.type}")
        if event.type == "TEXT_MESSAGE_CONTENT":
            print(f"Content: {event.delta}")
```

## Testing Strategy

### Unit Testing
- Event transformation accuracy
- State synchronization correctness
- Error handling robustness
- HTTP client reliability

### Integration Testing
- End-to-end workflow execution
- Real MCP service communication
- Frontend compatibility validation
- Performance under load

### Validation Criteria
- ✅ All AG-UI events properly mapped
- ✅ Real-time streaming without delays
- ✅ State consistency maintained
- ✅ Error recovery mechanisms working
- ✅ Performance meets requirements (< 2s response time)

## Deployment Guide

### Prerequisites
- Python 3.8+
- MCP Financial Analyzer service running
- Required dependencies installed

### Installation Steps

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Configure environment
export MCP_SERVICE_URL="http://localhost:8080"
export AG_UI_BRIDGE_PORT="8081"

# 3. Start bridge service
python -m ag_ui_intergration.bridge.server --port 8081

# 4. Verify health
curl http://localhost:8081/health
```

### Configuration Options

```yaml
# config.yaml
bridge:
  mcp_service_url: "http://localhost:8080"
  port: 8081
  host: "0.0.0.0"
  timeout: 300
  max_retries: 3

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

cors:
  origins: ["http://localhost:3000"]
  methods: ["GET", "POST", "OPTIONS"]
  headers: ["Content-Type", "Authorization"]
```

## Detailed Event Transformation Specifications

### Research Phase Event Mapping

```python
# MCP Research Agent Events -> AG-UI Events
def transform_research_events(mcp_event):
    if mcp_event.type == "research_started":
        return [
            TextMessageStartEvent(messageId="research_msg", role="assistant"),
            TextMessageContentEvent(
                messageId="research_msg",
                delta="🔍 Starting financial research..."
            ),
            StateDeltaEvent(delta=[{
                "op": "replace",
                "path": "/analysisPhase",
                "value": "research"
            }])
        ]

    elif mcp_event.type == "search_query":
        return [
            ToolCallStartEvent(
                toolCallId="search_" + uuid.uuid4().hex[:8],
                toolCallName="google_search",
                parentMessageId="research_msg"
            ),
            ToolCallArgsEvent(
                toolCallId="search_" + uuid.uuid4().hex[:8],
                delta=json.dumps({"query": mcp_event.query})
            )
        ]
```

### Analysis Phase Event Mapping

```python
# MCP Analysis Agent Events -> AG-UI Events
def transform_analysis_events(mcp_event):
    if mcp_event.type == "analysis_started":
        return [
            TextMessageContentEvent(
                messageId="analysis_msg",
                delta="📊 Analyzing financial data..."
            ),
            StateDeltaEvent(delta=[{
                "op": "replace",
                "path": "/analysisPhase",
                "value": "analysis"
            }, {
                "op": "replace",
                "path": "/progress",
                "value": 50
            }])
        ]
```

## Error Handling Specifications

### Error Categories and Responses

| Error Category | AG-UI Response | Recovery Action |
|----------------|----------------|-----------------|
| Network Timeout | `RUN_ERROR` with retry suggestion | Automatic retry with exponential backoff |
| Invalid Input | `RUN_ERROR` with validation details | Request user to correct input |
| MCP Service Down | `RUN_ERROR` with service status | Health check and service restart |
| Analysis Failure | `RUN_ERROR` with partial results | Offer to retry with different parameters |

### Error Event Structure

```python
class ErrorEvent:
    type: str = "RUN_ERROR"
    error: {
        "code": str,           # Error code (e.g., "NETWORK_TIMEOUT")
        "message": str,        # Human-readable message
        "details": dict,       # Technical details
        "recovery": list,      # Suggested recovery actions
        "timestamp": str       # ISO timestamp
    }
```

## Performance Specifications

### Response Time Requirements
- Initial response: < 500ms
- First content chunk: < 2s
- Complete analysis: < 5 minutes
- State updates: < 100ms

### Throughput Requirements
- Concurrent requests: 10+ simultaneous analyses
- Event streaming rate: 100+ events/second
- Memory usage: < 512MB per analysis
- CPU usage: < 50% during peak load

## Security Considerations

### Authentication
- API key-based authentication for MCP service
- JWT tokens for frontend authentication
- Rate limiting per client/IP address

### Data Protection
- No sensitive financial data stored in logs
- Encrypted communication (HTTPS/WSS)
- Input sanitization and validation
- Output filtering for sensitive information

## Monitoring and Observability

### Metrics to Track
- Request/response latencies
- Error rates by category
- Active analysis sessions
- Resource utilization
- Event streaming performance

### Logging Strategy
- Structured JSON logging
- Correlation IDs for request tracking
- Different log levels for development/production
- Sensitive data redaction

## Appendices

### A. Complete Event Schema Definitions
[Reference to existing AG-UI event schemas in the codebase]

### B. MCP Service API Documentation
[Reference to MCP Financial Analyzer API documentation]

### C. Frontend Integration Examples
[Code examples for different frontend frameworks]

### D. Troubleshooting Guide
[Common issues and solutions]

---

**Implementation Status:**
- ✅ Analysis Phase Complete
- ✅ Architecture Design Complete
- ✅ Event Mapping Specification Complete
- ⏳ Implementation Planning In Progress
- ⏳ Technical Documentation In Progress

**Next Steps:**
1. Review and approve this specification
2. Begin Phase 1 implementation with core bridge components
3. Set up development environment and testing infrastructure
4. Create initial test cases and validation criteria
5. Implement monitoring and observability features
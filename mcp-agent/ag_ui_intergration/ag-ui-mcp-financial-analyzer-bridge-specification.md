# AG-UI to MCP Financial Analyzer Bridge - Technical Specification & Implementation Roadmap

## Table of Contents
1. [Executive Summary](#executive-summary)
2. [Analysis Findings](#analysis-findings)
3. [Architecture Design](#architecture-design)
4. [Implementation Plan](#implementation-plan)
5. [Testing Strategy](#testing-strategy)
6. [Deployment Guide](#deployment-guide)
7. [API Reference](#api-reference)
8. [Configuration](#configuration)

---

## Executive Summary

This document provides a comprehensive technical specification and implementation roadmap for creating a bridge/middleware that connects AG-UI events to the MCP Financial Analyzer service. The solution enables real-time financial analysis through the AG-UI protocol while leveraging the powerful MCP (Model Context Protocol) agent framework.

### Key Outcomes
- **Real-time Financial Analysis**: Stream financial data analysis through AG-UI protocol
- **Seamless Integration**: Bridge AG-UI events to MCP Financial Analyzer without code changes
- **Production Ready**: Comprehensive error handling, logging, and monitoring
- **Scalable Architecture**: Modular design supporting multiple financial analysis workflows

---

## Analysis Findings

### 1. AG-UI System Analysis

#### Core Architecture
The AG-UI system uses a middleware pattern extending `AbstractAgent` class with RxJS Observable-based event streaming.

```typescript
// Key AG-UI Interface
export class AbstractAgent {
  protected run(input: RunAgentInput): Observable<BaseEvent>
}

interface RunAgentInput {
  threadId: string;
  runId: string;
  messages: Message[];
  tools: Tool[];
}
```

#### Event Types & Flow
```typescript
enum EventType {
  RUN_STARTED = "RUN_STARTED",
  RUN_FINISHED = "RUN_FINISHED", 
  RUN_ERROR = "RUN_ERROR",
  TEXT_MESSAGE_START = "TEXT_MESSAGE_START",
  TEXT_MESSAGE_CONTENT = "TEXT_MESSAGE_CONTENT",
  TEXT_MESSAGE_END = "TEXT_MESSAGE_END",
  TOOL_CALL_START = "TOOL_CALL_START",
  TOOL_CALL_ARGS = "TOOL_CALL_ARGS",
  TOOL_CALL_END = "TOOL_CALL_END"
}
```

#### Integration Requirements
- **Input Format**: `RunAgentInput` with thread tracking
- **Output Format**: Observable stream of `BaseEvent` objects
- **Tool Integration**: Map tools to AG-UI tool call events
- **State Management**: Real-time state updates via JSON Patch

### 2. MCP Financial Analyzer Analysis

#### Core Components
The MCP Financial Analyzer implements a multi-agent orchestration pattern:

```python
# Main Components
class MCPApp:                    # Global state and configuration
class Agent:                     # MCP agent with server access
class Orchestrator:              # Workflow coordination
class EvaluatorOptimizerLLM:     # Quality control loop
```

#### Agent Architecture
```python
# Agent Factory Pattern
create_research_agent(company_name: str) -> BaseAgentWrapper
create_analyst_agent(company_name: str) -> BaseAgentWrapper  
create_report_writer(company_name: str, output_path: str) -> BaseAgentWrapper
```

#### MCP Server Integration
- **g-search**: Financial data search via Google
- **fetch**: Web content retrieval
- **filesystem**: Report generation and file operations

#### Existing AG-UI Support
The codebase already contains AG-UI integration components:
- `AGUIStateManager`: State management with JSON Patch
- `FinancialAnalysisEventStream`: Event streaming infrastructure
- `MCPToAGUIToolBridge`: Tool call translation
- `AGUIResearchAgent`, `AGUIAnalystAgent`, `AGUIReportWriter`: Agent wrappers

### 3. Event Mapping Analysis

#### AG-UI Events → MCP Functions
```typescript
// Event Flow Mapping
RUN_STARTED → initialize_financial_analysis()
TEXT_MESSAGE_* → stream_analysis_progress()
TOOL_CALL_* → execute_mcp_tool()
STATE_* → update_analysis_state()
RUN_FINISHED → complete_analysis()
RUN_ERROR → handle_analysis_error()
```

#### Tool Mapping
```typescript
// Tool Translation
{
  "search_financial_data": "g-search",
  "fetch_web_content": "fetch",
  "write_file": "filesystem_write",
  "read_file": "filesystem_read"
}
```

---

## Architecture Design

### 1. Middleware Architecture

#### High-Level Design
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AG-UI Client  │───▶│ Bridge/Middleware│───▶│ MCP Financial   │
│                 │    │                 │    │   Analyzer      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  Event Stream   │
                       │   Manager       │
                       └─────────────────┘
```

#### Component Architecture
```typescript
// Core Middleware Components
class FinancialAnalyzerAgent extends AbstractAgent {
  protected async run(input: RunAgentInput): Observable<BaseEvent>
}

class EventStreamManager {
  streamAnalysisProgress(progress: AnalysisProgress): void
  streamToolExecution(toolCall: ToolCall): void
  streamResults(results: AnalysisResults): void
}

class MCPServiceAdapter {
  async executeFinancialAnalysis(request: AnalysisRequest): Promise<AnalysisResults>
  async streamAnalysisUpdates(callback: EventCallback): void
}
```

### 2. Data Transformation Logic

#### AG-UI Input → MCP Request
```typescript
interface AGUIToMCPTransformer {
  transformRunInput(input: RunAgentInput): MCPAnalysisRequest
  transformMessages(messages: Message[]): MCPContext
  transformTools(tools: Tool[]): MCPToolConfig
}

// Implementation
class DataTransformer implements AGUIToMCPTransformer {
  transformRunInput(input: RunAgentInput): MCPAnalysisRequest {
    return {
      company_name: this.extractCompanyName(input.messages),
      thread_id: input.threadId,
      run_id: input.runId,
      analysis_type: this.determineAnalysisType(input.messages),
      mcp_context: this.transformMessages(input.messages),
      tool_config: this.transformTools(input.tools)
    }
  }
}
```

#### MCP Response → AG-UI Events
```typescript
interface MCPToAGUITransformer {
  transformAnalysisResults(results: MCPAnalysisResults): BaseEvent[]
  transformProgressUpdate(update: MCPProgressUpdate): BaseEvent
  transformError(error: MCPError): BaseEvent
}

// Event Generation
class EventGenerator implements MCPToAGUITransformer {
  transformAnalysisResults(results: MCPAnalysisResults): BaseEvent[] {
    return [
      this.createTextMessageStart(),
      ...this.createTextMessageContent(results.report_content),
      this.createTextMessageEnd(),
      this.createRunFinished(results)
    ]
  }
}
```

### 3. Error Handling Strategy

#### Error Types & Responses
```typescript
enum ErrorType {
  COMPANY_NOT_FOUND = "company_not_found",
  MCP_SERVER_ERROR = "mcp_server_error", 
  ANALYSIS_TIMEOUT = "analysis_timeout",
  INVALID_REQUEST = "invalid_request"
}

interface ErrorHandler {
  handleMCPError(error: MCPError): BaseEvent
  handleValidationError(error: ValidationError): BaseEvent
  handleTimeoutError(error: TimeoutError): BaseEvent
}
```

#### Retry & Fallback Logic
```typescript
class RetryManager {
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    backoffMs: number = 1000
  ): Promise<T>
  
  async executeWithFallback<T>(
    primary: () => Promise<T>,
    fallback: () => Promise<T>
  ): Promise<T>
}
```

---

## Implementation Plan

### Phase 1: Core Bridge Implementation (Week 1-2)

#### Task Breakdown
1. **Setup Project Structure**
   - Create bridge module directory
   - Setup TypeScript configuration
   - Configure build and development tools

2. **Implement AbstractAgent Extension**
   ```typescript
   // File: src/agents/FinancialAnalyzerAgent.ts
   export class FinancialAnalyzerAgent extends AbstractAgent {
     private mcpAdapter: MCPServiceAdapter;
     private eventManager: EventStreamManager;
     
     protected async run(input: RunAgentInput): Observable<BaseEvent> {
       // Implementation
     }
   }
   ```

3. **Create MCP Service Adapter**
   ```typescript
   // File: src/adapters/MCPServiceAdapter.ts
   export class MCPServiceAdapter {
     async executeFinancialAnalysis(request: AnalysisRequest): Promise<AnalysisResults>
     async streamAnalysisUpdates(callback: EventCallback): void
   }
   ```

4. **Implement Event Stream Manager**
   ```typescript
   // File: src/events/EventStreamManager.ts
   export class EventStreamManager {
     streamAnalysisProgress(progress: AnalysisProgress): void
     streamToolExecution(toolCall: ToolCall): void
     streamResults(results: AnalysisResults): void
   }
   ```

### Phase 2: Data Transformation (Week 2-3)

#### Task Breakdown
1. **Request Transformation**
   - AG-UI input validation
   - Message context extraction
   - Tool configuration mapping

2. **Response Transformation**
   - MCP result formatting
   - Event stream generation
   - Error response handling

3. **State Management**
   - Progress tracking
   - Session management
   - Context preservation

### Phase 3: Error Handling & Resilience (Week 3-4)

#### Task Breakdown
1. **Error Classification**
   - Define error types
   - Implement error handlers
   - Create fallback mechanisms

2. **Retry Logic**
   - Exponential backoff
   - Circuit breaker pattern
   - Timeout management

3. **Logging & Monitoring**
   - Structured logging
   - Metrics collection
   - Health checks

### Phase 4: Testing & Validation (Week 4-5)

#### Task Breakdown
1. **Unit Tests**
   - Component isolation tests
   - Data transformation tests
   - Error handling tests

2. **Integration Tests**
   - End-to-end workflow tests
   - MCP service integration tests
   - Event streaming tests

3. **Performance Tests**
   - Load testing
   - Concurrency testing
   - Memory usage analysis

### Phase 5: Deployment & Documentation (Week 5-6)

#### Task Breakdown
1. **Deployment Configuration**
   - Environment setup
   - Service configuration
   - Monitoring setup

2. **Documentation**
   - API documentation
   - Usage examples
   - Troubleshooting guide

3. **Production Readiness**
   - Security review
   - Performance optimization
   - Monitoring setup

---

## Testing Strategy

### 1. Unit Testing

#### Test Categories
```typescript
describe('FinancialAnalyzerAgent', () => {
  describe('run method', () => {
    it('should handle valid financial analysis request')
    it('should stream progress updates correctly')
    it('should handle MCP service errors gracefully')
    it('should validate input parameters')
  })
})

describe('MCPServiceAdapter', () => {
  describe('executeFinancialAnalysis', () => {
    it('should execute research phase successfully')
    it('should execute analysis phase successfully')
    it('should execute reporting phase successfully')
    it('should handle timeout errors')
  })
})
```

#### Test Implementation
```typescript
// File: tests/unit/FinancialAnalyzerAgent.test.ts
import { FinancialAnalyzerAgent } from '../src/agents/FinancialAnalyzerAgent'
import { MockMCPServiceAdapter } from './mocks/MockMCPServiceAdapter'

describe('FinancialAnalyzerAgent', () => {
  let agent: FinancialAnalyzerAgent
  let mockAdapter: MockMCPServiceAdapter

  beforeEach(() => {
    mockAdapter = new MockMCPServiceAdapter()
    agent = new FinancialAnalyzerAgent(mockAdapter)
  })

  it('should stream financial analysis for valid company', async () => {
    const input = createTestRunAgentInput('Apple Inc.')
    const events = await collectObservableEvents(agent.run(input))
    
    expect(events).toContainEventOfType('RUN_STARTED')
    expect(events).toContainEventOfType('TEXT_MESSAGE_START')
    expect(events).toContainEventOfType('TOOL_CALL_START')
    expect(events).toContainEventOfType('RUN_FINISHED')
  })
})
```

### 2. Integration Testing

#### Test Scenarios
```typescript
describe('End-to-End Financial Analysis', () => {
  it('should complete full analysis workflow for public company')
  it('should handle invalid company name gracefully')
  it('should stream real-time progress updates')
  it('should generate downloadable report')
})

describe('MCP Service Integration', () => {
  it('should execute g-search queries successfully')
  it('should fetch financial data from web sources')
  it('should save analysis report to filesystem')
})
```

### 3. Performance Testing

#### Load Testing
```typescript
describe('Performance Tests', () => {
  it('should handle 10 concurrent analysis requests')
  it('should complete analysis within 60 seconds')
  it('should maintain memory usage under 512MB')
  it('should handle network interruptions gracefully')
})
```

#### Benchmarking
```typescript
// File: tests/performance/benchmarks.ts
import { performance } from 'perf_hooks'

describe('Performance Benchmarks', () => {
  it('should complete analysis in under 30 seconds for S&P 500 companies', async () => {
    const startTime = performance.now()
    await runFinancialAnalysis('AAPL')
    const endTime = performance.now()
    
    expect(endTime - startTime).toBeLessThan(30000)
  })
})
```

---

## Deployment Guide

### 1. Prerequisites

#### System Requirements
- **Node.js**: v18+ for AG-UI components
- **Python**: v3.9+ for MCP services
- **Redis**: For session management (optional)
- **Docker**: For containerized deployment

#### Dependencies
```bash
# AG-UI Dependencies
npm install @ag-ui/client rxjs

# MCP Dependencies  
pip install mcp-agent uv

# Bridge Dependencies
npm install typescript @types/node
```

### 2. Installation Steps

#### Step 1: Clone and Setup
```bash
git clone <repository-url>
cd ag-ui-mcp-financial-bridge
npm install
```

#### Step 2: Configure Environment
```bash
# Create environment file
cp .env.example .env

# Edit configuration
vim .env
```

#### Step 3: Build and Start
```bash
# Build TypeScript
npm run build

# Start development server
npm run dev

# Start production server
npm run start
```

### 3. Configuration Files

#### AG-UI Configuration
```typescript
// File: config/agui.config.ts
export const aguiConfig = {
  port: 8080,
  host: 'localhost',
  corsOrigins: ['http://localhost:3000'],
  eventStreamConfig: {
    chunkSize: 50,
    chunkDelay: 0.05
  }
}
```

#### MCP Configuration
```yaml
# File: config/mcp_agent.config.yaml
execution_engine: asyncio
logger:
  transports: [file]
  level: debug
mcp:
  servers:
    g-search:
      command: "npx"
      args: ["-y", "g-search-mcp"]
    fetch:
      command: "uvx"
      args: ["mcp-server-fetch"]
    filesystem:
      command: "npx"
      args: ["-y", "@modelcontextprotocol/server-filesystem"]
```

### 4. Docker Deployment

#### Dockerfile
```dockerfile
# File: Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 8080
CMD ["npm", "start"]
```

#### Docker Compose
```yaml
# File: docker-compose.yml
version: '3.8'
services:
  ag-ui-bridge:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - MCP_CONFIG_PATH=/app/config/mcp_agent.config.yaml
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
```

---

## API Reference

### 1. Bridge API Endpoints

#### Financial Analysis Endpoint
```typescript
POST /api/financial-analysis
Content-Type: application/json

{
  "threadId": "thread_123",
  "runId": "run_456", 
  "company": "Apple Inc.",
  "analysisType": "comprehensive",
  "options": {
    "includeRiskAssessment": true,
    "generateReport": true
  }
}
```

#### Stream Events Endpoint
```typescript
GET /api/stream/{threadId}
Accept: text/event-stream

// Server-Sent Events stream
data: {"type": "RUN_STARTED", "threadId": "thread_123", ...}
data: {"type": "TEXT_MESSAGE_CONTENT", "delta": "Analyzing Apple Inc...", ...}
data: {"type": "RUN_FINISHED", "threadId": "thread_123", ...}
```

### 2. Event Schema Reference

#### Base Event Structure
```typescript
interface BaseEvent {
  type: EventType;
  timestamp: string;
  threadId: string;
  runId?: string;
  metadata?: Record<string, any>;
}
```

#### Specific Event Types
```typescript
interface RunStartedEvent extends BaseEvent {
  type: 'RUN_STARTED';
  input: RunAgentInput;
}

interface TextMessageContentEvent extends BaseEvent {
  type: 'TEXT_MESSAGE_CONTENT';
  messageId: string;
  delta: string;
}

interface ToolCallStartEvent extends BaseEvent {
  type: 'TOOL_CALL_START';
  toolCallId: string;
  toolCallName: string;
  parentMessageId?: string;
}
```

### 3. Tool Schema Reference

#### Available Tools
```typescript
interface FinancialAnalysisTools {
  search_financial_data: {
    name: 'search_financial_data';
    description: 'Search for financial data and market information';
    parameters: {
      query: string;
      search_type?: 'general' | 'stock_price' | 'earnings' | 'news';
    };
  };
  
  fetch_web_content: {
    name: 'fetch_web_content';
    description: 'Fetch content from financial websites';
    parameters: {
      url: string;
    };
  };
  
  write_file: {
    name: 'write_file';
    description: 'Write financial analysis report';
    parameters: {
      path: string;
      content: string;
    };
  };
}
```

---

## Configuration

### 1. Environment Variables

```bash
# AG-UI Configuration
AGUI_PORT=8080
AGUI_HOST=localhost
AGUI_CORS_ORIGINS=http://localhost:3000

# MCP Configuration
MCP_CONFIG_PATH=./config/mcp_agent.config.yaml
MCP_SECRETS_PATH=./config/mcp_agent.secrets.yaml

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/bridge.log

# Performance Configuration
MAX_CONCURRENT_ANALYSES=5
ANALYSIS_TIMEOUT_MS=60000
```

### 2. Runtime Configuration

#### Bridge Configuration
```typescript
// File: config/bridge.config.ts
export interface BridgeConfig {
  maxConcurrentAnalyses: number;
  analysisTimeoutMs: number;
  retryConfig: {
    maxRetries: number;
    backoffMs: number;
  };
  eventStreamConfig: {
    chunkSize: number;
    chunkDelay: number;
  };
}

export const bridgeConfig: BridgeConfig = {
  maxConcurrentAnalyses: 5,
  analysisTimeoutMs: 60000,
  retryConfig: {
    maxRetries: 3,
    backoffMs: 1000
  },
  eventStreamConfig: {
    chunkSize: 50,
    chunkDelay: 0.05
  }
};
```

### 3. Security Configuration

#### Authentication & Authorization
```typescript
// File: config/security.config.ts
export interface SecurityConfig {
  enableAuth: boolean;
  apiKeys: string[];
  rateLimiting: {
    windowMs: number;
    maxRequests: number;
  };
  cors: {
    origins: string[];
    methods: string[];
  };
}
```

---

## Appendix

### A. Code Examples

#### Complete Agent Implementation
```typescript
// File: src/agents/FinancialAnalyzerAgent.ts
import { AbstractAgent, BaseEvent, RunAgentInput, EventType } from '@ag-ui/client';
import { Observable } from 'rxjs';
import { MCPServiceAdapter } from '../adapters/MCPServiceAdapter';
import { EventStreamManager } from '../events/EventStreamManager';

export class FinancialAnalyzerAgent extends AbstractAgent {
  private mcpAdapter: MCPServiceAdapter;
  private eventManager: EventStreamManager;

  constructor() {
    super();
    this.mcpAdapter = new MCPServiceAdapter();
    this.eventManager = new EventStreamManager();
  }

  protected run(input: RunAgentInput): Observable<BaseEvent> {
    return new Observable<BaseEvent>((observer) => {
      this.executeAnalysis(input, observer);
    });
  }

  private async executeAnalysis(
    input: RunAgentInput,
    observer: any
  ): Promise<void> {
    try {
      // Emit RUN_STARTED
      observer.next({
        type: EventType.RUN_STARTED,
        threadId: input.threadId,
        runId: input.runId,
        timestamp: new Date().toISOString()
      });

      // Extract company name from messages
      const companyName = this.extractCompanyName(input.messages);
      
      // Execute MCP financial analysis
      const results = await this.mcpAdapter.executeFinancialAnalysis({
        company_name: companyName,
        thread_id: input.threadId,
        run_id: input.runId
      });

      // Stream results as text messages
      await this.eventManager.streamResults(results, observer, input.threadId);

      // Emit RUN_FINISHED
      observer.next({
        type: EventType.RUN_FINISHED,
        threadId: input.threadId,
        runId: input.runId,
        timestamp: new Date().toISOString()
      });

      observer.complete();
    } catch (error) {
      observer.next({
        type: EventType.RUN_ERROR,
        threadId: input.threadId,
        runId: input.runId,
        message: error.message,
        timestamp: new Date().toISOString()
      });
      observer.error(error);
    }
  }

  private extractCompanyName(messages: any[]): string {
    // Extract company name from user messages
    const userMessage = messages.find(m => m.role === 'user');
    return userMessage?.content || 'Apple Inc.';
  }
}
```

### B. Testing Utilities

#### Mock Service Adapter
```typescript
// File: tests/mocks/MockMCPServiceAdapter.ts
export class MockMCPServiceAdapter {
  async executeFinancialAnalysis(request: any): Promise<any> {
    // Return mock analysis results
    return {
      status: 'success',
      company: request.company_name,
      analysis: {
        stock_performance: { /* mock data */ },
        earnings_analysis: { /* mock data */ },
        market_context: { /* mock data */ }
      },
      report_path: '/tmp/mock_report.md'
    };
  }
}
```

### C. Monitoring & Observability

#### Metrics Collection
```typescript
// File: src/monitoring/metrics.ts
export class MetricsCollector {
  private analysisCount = 0;
  private errorCount = 0;
  private averageAnalysisTime = 0;

  recordAnalysisStart(): void {
    this.analysisCount++;
  }

  recordAnalysisError(): void {
    this.errorCount++;
  }

  recordAnalysisTime(timeMs: number): void {
    this.averageAnalysisTime = 
      (this.averageAnalysisTime + timeMs) / this.analysisCount;
  }

  getMetrics(): any {
    return {
      analysisCount: this.analysisCount,
      errorCount: this.errorCount,
      errorRate: this.errorCount / this.analysisCount,
      averageAnalysisTime: this.averageAnalysisTime
    };
  }
}
```

---

## Conclusion

This comprehensive specification provides a complete roadmap for implementing a production-ready bridge between AG-UI and the MCP Financial Analyzer. The architecture is designed to be scalable, maintainable, and performant while providing real-time streaming capabilities for financial analysis workflows.

The implementation follows best practices for error handling, testing, and monitoring, ensuring a robust solution suitable for production deployment.

For questions or clarifications, please refer to the respective documentation sections or contact the development team.

---

*Generated by Claude Code - AG-UI MCP Financial Analyzer Bridge Specification*
*Version: 1.0.0*
*Last Updated: 2025-01-08*
{"$schema": "https://turbo.build/schema.json", "ui": "stream", "tasks": {"build": {"cache": false, "persistent": false, "interactive": false, "dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "@copilotkit/runtime#generate-graphql-schema": {"cache": false, "persistent": false, "dependsOn": ["^build"]}, "@copilotkit/runtime-client-gql#graphql-codegen": {"cache": false, "persistent": false, "dependsOn": ["^build", "@copilotkit/runtime#generate-graphql-schema"]}, "dev": {"dependsOn": ["^build"], "cache": false, "persistent": true}, "test": {"cache": false, "dependsOn": ["build"], "outputs": ["coverage/**", "!.next/**", "!.next/cache/**"]}, "lint": {}, "check-types": {"cache": false, "dependsOn": ["build"]}, "clean": {"cache": false}, "//#check-prettier": {}, "//#format": {"cache": false}, "//#freshbuild": {"cache": false}, "link:global": {"cache": false, "persistent": false}, "unlink:global": {"cache": false, "persistent": false}}}
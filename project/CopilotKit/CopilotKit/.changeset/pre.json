{"mode": "pre", "tag": "next", "initialVersions": {"next-openai": "1.4.6", "next-pages-router": "1.4.6", "node-express": "1.4.6", "node-http": "1.4.6", "@copilotkit/react-core": "1.9.1", "@copilotkit/react-textarea": "1.9.1", "@copilotkit/react-ui": "1.9.1", "@copilotkit/runtime": "1.9.1", "@copilotkit/runtime-client-gql": "1.9.1", "@copilotkit/sdk-js": "1.9.1", "@copilotkit/shared": "1.9.1", "eslint-config-custom": "1.4.6", "tailwind-config": "1.4.6", "tsconfig": "1.4.6"}, "changesets": ["big-bears-grow", "bright-ravens-pump", "happy-camels-tan", "hip-ghosts-drum", "hungry-carrots-count", "late-eyes-drive", "ninety-cameras-prove", "ninety-items-scream", "plenty-cows-kiss", "rare-poems-count", "seven-grapes-wonder", "six-pumas-explain", "slimy-mice-marry", "tender-geese-look", "thirty-trees-glow", "weak-bats-hear"]}
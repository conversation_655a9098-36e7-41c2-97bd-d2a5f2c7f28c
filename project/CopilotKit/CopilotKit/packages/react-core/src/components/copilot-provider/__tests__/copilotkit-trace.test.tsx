/// <reference types="jest" />
import { CopilotTraceEvent, CopilotTraceHandler } from "@copilotkit/shared";

describe("CopilotKit onTrace types", () => {
  let mockTrace: jest.Mock;

  beforeEach(() => {
    mockTrace = jest.fn();
  });

  describe("onTrace type checking", () => {
    it("should accept CopilotTraceHandler type", () => {
      // RED: Will fail - testing type compatibility
      const handler: CopilotTraceHandler = mockTrace;
      expect(handler).toBeDefined();
    });

    it("should validate trace event structure", () => {
      // RED: Will fail - validating event structure
      const mockEvent: CopilotTraceEvent = {
        type: "error",
        timestamp: Date.now(),
        context: {
          source: "ui",
          request: {
            operation: "test",
            startTime: Date.now(),
          },
        },
      };

      expect(mockEvent.type).toBe("error");
      expect(mockEvent.timestamp).toBeDefined();
      expect(mockEvent.context.source).toBe("ui");
    });

    it("should validate trace event types", () => {
      // RED: Will fail - ensuring all event types are valid
      const validTypes: CopilotTraceEvent["type"][] = [
        "error",
        "request",
        "response",
        "agent_state",
        "action",
        "message",
        "performance",
      ];

      expect(validTypes).toContain("error");
      expect(validTypes).toContain("request");
      expect(validTypes).toContain("agent_state");
    });
  });

  describe("trace handler functionality", () => {
    it("should accept trace handler with proper signature", () => {
      // RED: Will fail - testing handler function signature
      const handler: CopilotTraceHandler = (traceEvent: CopilotTraceEvent) => {
        console.log("Trace:", traceEvent.type);
      };

      expect(typeof handler).toBe("function");
    });

    it("should handle async trace handlers", async () => {
      // RED: Will fail - testing async handler support
      const asyncHandler: CopilotTraceHandler = async (traceEvent: CopilotTraceEvent) => {
        await Promise.resolve();
        return;
      };

      expect(typeof asyncHandler).toBe("function");
    });
  });
});

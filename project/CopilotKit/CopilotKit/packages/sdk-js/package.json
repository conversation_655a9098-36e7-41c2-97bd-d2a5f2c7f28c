{"name": "@copilotkit/sdk-js", "private": false, "homepage": "https://github.com/CopilotKit/CopilotKit", "repository": {"type": "git", "url": "https://github.com/CopilotKit/CopilotKit.git"}, "publishConfig": {"access": "public"}, "version": "1.9.2-next.14", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./langchain": {"types": "./dist/langchain.d.ts", "require": "./dist/langchain.js", "import": "./dist/langchain.mjs"}, "./langgraph": {"types": "./dist/langgraph.d.ts", "require": "./dist/langgraph.js", "import": "./dist/langgraph.mjs"}}, "types": "./dist/index.d.ts", "license": "MIT", "scripts": {"build": "tsup --clean", "dev": "tsup --watch --no-splitting", "test": "jest --passWithNoTests", "check-types": "tsc --noEmit", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist && rm -rf .next", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "devDependencies": {"@swc/core": "1.5.28", "@types/express": "^4.17.21", "@types/jest": "^29.5.4", "@types/node": "^18.11.17", "@whatwg-node/server": "^0.9.34", "eslint": "^8.56.0", "eslint-config-custom": "workspace:*", "jest": "^29.6.4", "nodemon": "^3.1.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig": "workspace:*", "tsup": "^6.7.0", "typescript": "^5.2.3", "zod-to-json-schema": "^3.23.5"}, "dependencies": {"@copilotkit/shared": "workspace:*", "@langchain/community": "^0.0.53", "@langchain/core": "^0.3.13", "@langchain/langgraph": "0.2.44", "langchain": "^0.3.3", "zod": "^3.23.3"}, "keywords": ["copilotkit", "copilot", "react", "nextjs", "nodejs", "ai", "assistant", "javascript", "automation", "textarea"]}
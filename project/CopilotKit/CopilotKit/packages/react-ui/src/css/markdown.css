h1.copilotKitMarkdownElement,
h2.copilotKitMarkdownElement,
h3.copilotKitMarkdownElement,
h4.copilotKitMarkdownElement,
h5.copilotKitMarkdownElement,
h6.copilotKitMarkdownElement {
  font-weight: bold;
  line-height: 1.2;
}

h1.copilotKitMarkdownElement:not(:last-child),
h2.copilotKitMarkdownElement:not(:last-child),
h3.copilotKitMarkdownElement:not(:last-child),
h4.copilotKitMarkdownElement:not(:last-child),
h5.copilotKitMarkdownElement:not(:last-child),
h6.copilotKitMarkdownElement:not(:last-child) {
  margin-bottom: 1rem;
}

h1.copilotKitMarkdownElement {
  font-size: 1.5em;
}

h2.copilotKitMarkdownElement {
  font-size: 1.25em;
  font-weight: 600;
}

h3.copilotKitMarkdownElement {
  font-size: 1.1em;
}

h4.copilotKitMarkdownElement {
  font-size: 1em;
}

h5.copilotKitMarkdownElement {
  font-size: 0.9em;
}

h6.copilotKitMarkdownElement {
  font-size: 0.8em;
}

a.copilotKitMarkdownElement {
  color: blue;
  text-decoration: underline;
}

p.copilotKitMarkdownElement {
  padding: 0px;
  margin: 0px;
  line-height: 1.75;
  font-size: 1rem;
}

p.copilotKitMarkdownElement:not(:last-child),
pre.copilotKitMarkdownElement:not(:last-child),
ol.copilotKitMarkdownElement:not(:last-child),
ul.copilotKitMarkdownElement:not(:last-child),
blockquote.copilotKitMarkdownElement:not(:last-child) {
  margin-bottom: 1.25em;
}

blockquote.copilotKitMarkdownElement {
  border-color: rgb(142, 142, 160);
  border-left-width: 2px;
  border-left-style: solid;
  line-height: 1.2;
  padding-left: 10px;
}

blockquote.copilotKitMarkdownElement p {
  padding: 0.7em 0;
}

ul.copilotKitMarkdownElement {
  list-style-type: disc;
  padding-left: 20px;
  overflow: visible;
}

li.copilotKitMarkdownElement {
  list-style-type: inherit;
  list-style-position: outside;
  margin-left: 0;
  padding-left: 0;
  position: relative;
  overflow: visible;
}

.copilotKitCodeBlock {
  position: relative;
  width: 100%;
  background-color: rgb(9 9 11);
  border-radius: 0.375rem;
}

.copilotKitCodeBlockToolbar {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  background-color: rgb(39 39 42);
  padding-left: 1rem;
  padding-top: 0.09rem;
  padding-bottom: 0.09rem;
  color: rgb(228, 228, 228);
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  font-family: sans-serif;
}

.copilotKitCodeBlockToolbarLanguage {
  font-size: 0.75rem;
  line-height: 1rem;
  text-transform: lowercase;
}

.copilotKitCodeBlockToolbarButtons {
  display: flex;
  align-items: center;
  margin-right: 0.25rem;
  margin-left: 0.25rem;
}

.copilotKitCodeBlockToolbarButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  height: 2.5rem;
  padding: 3px;
  margin: 2px;
}

.copilotKitCodeBlockToolbarButton:hover {
  background-color: rgb(55, 55, 58);
}

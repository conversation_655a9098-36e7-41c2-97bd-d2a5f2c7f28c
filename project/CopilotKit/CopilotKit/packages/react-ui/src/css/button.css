.copilotKitButton {
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--copilot-kit-primary-color);
  outline: none;
  position: relative;
  transform: scale(1);
  transition: all 0.2s ease;
  background-color: var(--copilot-kit-primary-color);
  color: var(--copilot-kit-contrast-color);
  cursor: pointer;
  box-shadow: var(--copilot-kit-shadow-sm);
}

.copilotKitButton:hover {
  transform: scale(1.05);
  box-shadow: var(--copilot-kit-shadow-md);
}

.copilotKitButton:active {
  transform: scale(0.95);
  box-shadow: var(--copilot-kit-shadow-sm);
}

.copilotKitButtonIcon {
  transition:
    opacity 100ms,
    transform 300ms;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.copilotKitButtonIcon svg {
  width: 1.5rem;
  height: 1.5rem;
}

/* State when the chat is open */
.copilotKitButton.open .copilotKitButtonIconOpen {
  transform: translate(-50%, -50%) scale(0) rotate(90deg);
  opacity: 0;
}

.copilotKitButton.open .copilotKitButtonIconClose {
  transform: translate(-50%, -50%) scale(1) rotate(0deg);
  opacity: 1;
}

/* State when the chat is closed */
.copilotKitButton:not(.open) .copilotKitButtonIconOpen {
  transform: translate(-50%, -50%) scale(1) rotate(0deg);
  opacity: 1;
}

.copilotKitButton:not(.open) .copilotKitButtonIconClose {
  transform: translate(-50%, -50%) scale(0) rotate(-90deg);
  opacity: 0;
}

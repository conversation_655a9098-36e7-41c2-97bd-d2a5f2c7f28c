.copilotKitMessages footer .suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.copilotKitMessages footer h6 {
  font-weight: 500;
  font-size: 0.7rem;
  margin-bottom: 8px;
}

.copilotKitMessages footer .suggestions .suggestion {
  padding: 6px 10px;
  font-size: 0.7rem;
  border-radius: 15px;
  border: 1px solid var(--copilot-kit-muted-color);
  color: var(--copilot-kit-secondary-contrast-color);
  box-shadow: 0 5px 5px 0px rgba(0,0,0,.01),0 2px 3px 0px rgba(0,0,0,.02);
}

.copilotKitMessages footer .suggestions .suggestion.loading {
  padding: 0;
  font-size: 0.7rem;
  border: none;
  color: var(--copilot-kit-secondary-contrast-color);
}

.copilotKitMessages footer .suggestions button {
  transition: transform 0.3s ease;
}

.copilotKitMessages footer .suggestions button:not(:disabled):hover {
  transform: scale(1.03);
}

.copilotKitMessages footer .suggestions button:disabled {
  cursor: wait;
}

.copilotKitMessages footer .suggestions button svg {
  margin-right: 6px;
}

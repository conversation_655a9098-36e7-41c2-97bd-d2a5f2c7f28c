name: CI

on:
  push:
    branches: [main]
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - 'examples/**'
      - '.github/workflows/demos_preview.yml'
      - '.github/workflows/release.yml'
      - 'CopilotKit/packages/**/package.json'
      - 'CopilotKit/packages/**/CHANGELOG.md'
      - 'CopilotKit/.changeset/**'
  pull_request:
    branches: [main]
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - 'examples/**'
jobs:
  test:
    name: 'Test'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: 'CopilotKit'
    strategy:
      matrix:
        node-version: [20.x, 18.x]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: "9.5"
    
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build
        run: npx turbo run build

      - name: Run tests
        run: npx turbo run test

name: Quality

on:
  push:
    branches: [main]
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - 'examples/**'
      - '.github/workflows/demos_preview.yml'
      - '.github/workflows/release.yml'
      - 'CopilotKit/packages/**/package.json'
      - 'CopilotKit/packages/**/CHANGELOG.md'
      - 'CopilotKit/.changeset/**'
  pull_request:
    branches: [main]    
    paths-ignore:
      - 'docs/**'
      - 'README.md'
      - 'examples/**'

jobs:
  prettier:
    name: 'Prettier'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: 'CopilotKit'

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: "9.5"

      - name: Use Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20.x
          cache: 'pnpm'
          cache-dependency-path: '**/pnpm-lock.yaml'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run Prettier check
        run: npx turbo run check-prettier

  eslint:
    name: 'ESLint'
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: CopilotKit
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: "9.5"

      - name: Use Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20.x
          cache: 'pnpm'
          cache-dependency-path: '**/pnpm-lock.yaml'
        
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run ESLint check
        run: npx turbo run lint
{"name": "@changesets/action", "version": "1.4.9", "main": "dist/index.js", "license": "MIT", "devDependencies": {"@changesets/changelog-github": "^0.4.2", "@changesets/cli": "^2.20.0", "@changesets/write": "^0.1.6", "@vercel/ncc": "^0.36.1", "fixturez": "^1.1.0", "prettier": "^2.0.5", "typescript": "^5.0.4", "@babel/core": "^7.13.10", "@babel/preset-env": "^7.13.10", "@babel/preset-typescript": "^7.13.0", "@types/fs-extra": "^8.0.0", "@types/jest": "^29.5.1", "@types/node": "^20.11.17", "@types/semver": "^7.5.0", "babel-jest": "^29.5.0", "husky": "^3.0.3", "jest": "^29.5.0"}, "scripts": {"build": "ncc build src/index.ts -o dist --transpile-only --minify", "test": "jest", "test:watch": "yarn test --watch", "changeset": "changeset", "bump": "node ./scripts/bump.js", "release": "node ./scripts/release.js"}, "engines": {"node": ">= 20"}, "dependencies": {"@actions/core": "^1.10.0", "@actions/exec": "^1.1.1", "@actions/github": "^5.1.1", "@changesets/pre": "^1.0.9", "@changesets/read": "^0.5.3", "@manypkg/get-packages": "^1.1.3", "@octokit/plugin-throttling": "^5.2.1", "fs-extra": "^8.1.0", "mdast-util-to-string": "^1.0.6", "remark-parse": "^7.0.1", "remark-stringify": "^7.0.3", "resolve-from": "^5.0.0", "semver": "^7.5.3", "unified": "^8.3.2"}, "husky": {"hooks": {}}, "prettier": {}, "resolutions": {"**/@octokit/core": "4.2.0", "trim": "^0.0.3", "y18n": "^4.0.1"}}
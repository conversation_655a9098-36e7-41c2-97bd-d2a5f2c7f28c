
![hacktoberfest_banner](https://github.com/user-attachments/assets/813f2f32-999e-4e91-a527-9c1748380154)




## Hacktoberfest: Contribute to CopilotKit and Win Exclusive Swag!

🤩 Welcome to CopilotKit's Hacktoberfest event!

Participate by creating innovative demos for CopilotKit and you could win exclusive swag!

Please show your support and give a ⭐ to the CopilotKit repository.

## How to Participate in 4 Easy Steps

1. **Project Setup and Guidelines**: Refer to the [CONTRIBUTING.md](./CONTRIBUTING.md) file for detailed setup instructions and guidelines.
2. **Choose a Topic**: Visit our [Issues section](https://github.com/CopilotKit/CopilotKit/issues) tagged with `hacktoberfest`. CopilotKit will allow multi-assign!
3. **Submit Your Demo**: Create and submit your project according to the steps listed in the `CONTRIBUTING.md`.
4. **Add Your Project**: Don't forget to add your project to the `demos` folder to be part of our contributors' list!

## Why Join?

- 🥳 Win exclusive CopilotKit swag.
- 🎊 Engage with fun and exciting projects.
- 🎉 Showcase your skills and gain visibility.

## Rankings and Swag

![Swag_2](https://github.com/user-attachments/assets/3dd192e9-0a45-4e7d-a1bf-f7c71a1f0651)


Demos will be ranked from 0 to 100 based on:

| Criteria                  | Points |
| ------------------------- | ------ |
| Innovation & Creativity   | 0-25   |
| Usefulness & Practicality | 0-25   |
| Quality & Completeness    | 0-25   |
| User Experience & Design  | 0-25   |

## Bonus Points

⭐ 4 Ways to Earn 10 bonus points for each (Swag is only available if you complete a demo) :

1. ⭐ Write and share a tutorial about your app on [dev.to](https://dev.to) that receives 30 or more reactions.
2. ⭐ Post about your project on Twitter and get at least 10 reactions.
3. ⭐ Solve `good first issues` that get  merged.
4. ⭐ Create a YouTube video promoting your CopilotKit project that receives up to 100 views.

## Need Help?

- **Questions**: Use our [Discord support channel](https://discord.com/invite/6dffbvGU3D) for any questions you have.
- **Resources**: Visit [CopilotKit documentation](https://docs.copilotkit.ai/?ref=github_readme) for more helpful documentatation info.

⭐ Happy coding, and we look forward to your contributions!

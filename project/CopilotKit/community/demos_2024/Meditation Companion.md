

# 1. Meditation Companion
- **Meditation Prompt Display**: This app offers users calming meditation prompts, providing a peaceful space for mindfulness and introspection. Each prompt updates with an aesthetically pleasing real-time clock and date display.

- **Prompt Generation and Interaction**: Users can generate a new meditation prompt by pressing the "Generate New Meditation" button. Additionally, users can like and share their favorite prompts, promoting a sense of community and mindfulness. The prompts are displayed within a visually relaxing interface.

## 2. Name of Issue/Topic

⭐  - Meditation Companion App (Hacktoberfest Demo) 
## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)
- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [CopilotKit](https://copilotkit.ai)
- [ShadCN](https://ui.shadcn.com)
- ShadCN-UI Component Styling

## 4. GitHub Project Link

Click here - [Meditation Companion Repository](https://github.com/PentesterPriyanshu/meditation-companion)

### 5. Live Link

- [Live app](https://meditation-companion.vercel.app/)

- ![image](https://github.com/user-attachments/assets/cd8cb698-f59b-464a-919c-90a7a59de698)



## 7. Who am I?

I am a developer passionate about creating mindfulness and wellness applications that enhance mental well-being.
## 8.Social links

[github](https://github.com/PentesterPriyanshu/)

---

## 1. 🎵 Music Lyrics Finder 🎵

## 2. ⭕ Name of Issue/Topic

[#641 Develop a bot that finds and displays lyrics to any song based on user input](https://github.com/CopilotKit/CopilotKit/issues/641)

## 3. 🍵 Technologies Being Used

- **Frontend**: [React](https://reactjs.org/) - A JavaScript library for building user interfaces, known for its component-based architecture and efficient rendering.
- **Backend**: [CopilotKit](https://www.copilotkit.ai/) - A framework for building custom AI copilots, including in-app chatbots and AI-powered text areas.
- **Framework**: [Next.js v15](https://nextjs.org/) - A powerful React framework that simplifies server-side rendering and static site generation, enabling faster web development and enhanced performance for modern applications.
- **Styling**: [Shadcn UI](https://ui.shadcn.com/) - A component library built on top of Tailwind CSS, offering pre-designed UI components that streamline the development of stylish and accessible interfaces.

## 4. 🎡 GitHub Project Link

- **https://github.com/ArnavK-09/music_lyrics_finder/**

## 5 🎀 Live App

- **https://music-lyrics-finder.vercel.app/**

## 6. 📷 Screenshot

![screenshot](https://github.com/user-attachments/assets/5f6b9668-6b3f-48a1-bf72-91a9e6810e49)

## 7. 👨‍💻 Who Are You?

- [**` ArnavK-09 `**](https://gitHub.com/ArnavK-09)

## Project Title

[Snippy](https://snippy-self.vercel.app) - A code snippet generator and manager for developers

Snippy is a code snippet manager that allows you to store and manage your code snippets in one place. <PERSON><PERSON><PERSON> uses CopilotKit to provide features for AI-powered code completion, code generation, and automation.

## Name of Issue/Topic

Code Snippet Generator ( Hacktoberfest Demo )

## Technologies Being Used

- Next.js: React Framework
- React: JavaScript library for building user interfaces
- CopilotKit: Ai Copilots
- TypeScript: JavaScript with types
- TailwindCSS: utility-first CSS framework
- Shadncn: UI Component Styling
- v0: For AI based component generation
- Zustand: State Management

## App Link

- [Snippy Live Demo](https://snippy-self.vercel.app)
- [Repo](https://github.com/RohittCodes/snippy)

Deploy your app on [Vercel](https://vercel.com/new)

## Bonus Points
- Youtube video: https://www.youtube.com/watch?v=Vs88ft7mKx8
- X post: https://x.com/RohittCodes/status/1844960454445236423

## Screenshots

### CopilotSidebar to create, update, and delete snippets
![Screenshot 2024-10-02 193426](https://github.com/user-attachments/assets/690da14e-6131-462d-be46-f21b65f99c15)

### CopilotTextArea for code autocompletion
![Screenshot 2024-10-02 194525](https://github.com/user-attachments/assets/d0a12de4-8bbd-4efa-8782-b2c80dfb7172)

### CopilotChat to understand the snippets
![Screenshot 2024-10-02 193641](https://github.com/user-attachments/assets/4f2f6a73-b0ec-486c-a9cd-a370f72c6aa8)


## Who Are You?

- [RohittCodes](https://github.com/RohittCodes)
- [RohittCodes on X](https://x.com/RohittCodes)

## Snippet Sorcerer

## Name of Issue/Topic

⭐ 31 - Code Snippet Generator (Hacktoberfest Demo) #678

This demo addresses the challenge of efficiently generating code snippets using AI, tailored to user needs across various programming languages.

## Technologies Being Used

- Next.js
- v0 Styling
- Shadcn-UI Component Styling
- CopilotKit

## App Link

If your app is live, include the link here:
[Live Demo](https://snippet-generator-jet.vercel.app/)

Deploy your app on [Vercel](https://snippet-generator-jet.vercel.app/)

GitHub Repo: (https://github.com/rohitdash08/snippet-generator.git)

## Bonus Points

## Screenshot

Include a screenshot of your demo in action:
![image](https://github.com/user-attachments/assets/f9c44bc4-68d5-449a-aa77-99df9db82207)
![image](https://github.com/user-attachments/assets/cca06df5-de92-4470-94cc-7f034bf45125)

## Who Are You?

Github: [rohitdash08](https://github.com/rohitdash08)

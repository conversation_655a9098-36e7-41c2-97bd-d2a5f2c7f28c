## 🚀 **Project Title**

Emotion Reads - A Simple Book Recommendation System that gives recommendation based on the users emotions described by the person.

### 📝 **Name of Issue/Topic**

[Book Recommendation System (Hacktoberfest Demo)](https://github.com/CopilotKit/CopilotKit/issues/610)

---

### 🛠️ **Technologies Being Used**

List all technologies, tools, and frameworks you are utilizing for the project:

- **Frameworks**: NextJS
- **Styling**: v0 Styling, Shadcn-UI Component Styling, TailwindCSS Styling
- **Developer Tools**: CopilotKit

> **Note**: Ensure consistency by adhering to the [v0 guidelines](https://v0.dev/docs).

---

### 🌐 **App Link**


[Live Demo](https://emotion-reads.vercel.app/)
[Repo Link](https://github.com/Edantuti/Emotion-Reads)

---

### 🎯 **Bonus Points**

[Twitter Post](https://x.com/edantuti11/status/1852036703177052493)

---

### 📸 **Screenshot**

![image](https://github.com/Edantuti/Emotion-Reads/blob/main/images/demo.png?raw=true)

---

### 🙋‍♂️ **Who Are You?**

[Github](https://github.com/Edantuti)
[Twitter/X](https://x.com/edantuti11)

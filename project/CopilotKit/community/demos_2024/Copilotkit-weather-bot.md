
# 1. Copilotkit-weather-Bot
This Next.js application is a weather forecasting tool that utilizes CopilotKit for
AI-powered interactions and Shadcn-UI for styling. It allows users to input a location and receive 
real-time weather information, including temperature, description, humidity, and wind speed.

## 2. Name of Issue/Topic

Weather Forecasting Bot (Hacktoberfest Demo)

## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)
- [Next.js](https://nextjs.org)
- [Recharts](https://recharts.org)
- [Tailwind CSS](https://tailwindcss.com)
- [CopilotKit](https://copilotkit.ai)
- [ShadCN](https://ui.shadcn.com)
- [Zustand](https://zustand.docs.pmnd.rs)
- [Groq](https://groq.com)

### Note: Please only use the [Shadcn/ui]([https://v0.dev/docs](https://ui.shadcn.com/docs/installation)) for styling components.

- v0 Styling
- Shadcn-UI Component Styling
- CopilotKit

## 4. GitHub Project Link

### GitHub Repo Link: 
[JanumalaAkhilendra/Copilotkit-weather-bot](https://github.com/JanumalaAkhilendra/Copilotkit-weather-bot)

### 5 bonus points

- If your app is live, include the link here:
- [Live app](https://copilotkit-weather-bot.vercel.app/)
- A youtube video demo of Copilotkit-weather-bot is available [here](https://youtu.be/F2wiIcNrwOg)
 
## 6. Screenshot

-Copilotchat
![Screenshot (384).png](https://github.com/JanumalaAkhilendra/Akfirstproject/blob/main/Screenshot%20(384).png)
-Copilotchat
![Screenshot (383).png](https://github.com/JanumalaAkhilendra/Akfirstproject/blob/main/Screenshot%20(383).png)

## 7. Who Are You?

Please list your **GitHub** and **Twitter** handles if you feel comfortable doing so. 

- [Janumala Akhilendra](https://github.com/JanumalaAkhilendra)
- [Akhilendra on X](https://x.com/Akhilendra_01)

## 5 Extra Bonus Points
Link to any bonus activities you did such as solving GitHub Issues or posting to Twitter, Dev.to, Medium, etc related to your project and CopilotKit.


#issue 602 : Build a chatbot that gives real-time weather updates using CopilotKit's AI interaction capabilities.

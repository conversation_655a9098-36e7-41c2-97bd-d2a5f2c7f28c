# 1. TO-DO List with Prioritization
In this project, I developed a TO-DO List with Prioritization using React for the frontend, Tailwind CSS for styling, and Shadcn UI for enhanced UI components.
### Method 1 - Using Form
- User can add Task using form with its detail description, date & time and also needs to add priority of task.
- User also show task completed as well as delete the task.
### Method 2 - Using Copilotkit
- User need to write task name,task details, due date and time as well as priority of task.

## 2. Name of Issue/Topic

⭐ 14 - To-Do List with Prioritization (Hacktoberfest Demo) #626

## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)
- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [CopilotKit](https://copilotkit.ai)
- [ShadCN](https://ui.shadcn.com)
- Shadcn-UI Component Styling
- CopilotKit

## 4. GitHub Project Link

### [Link-](https://github.com/Vaishnavi-<PERSON>kar/PriorityTodo---CopilotKit)

## 5. Bonus points

- If your app is live, include the link here:
- [Live app](https://priority-todolist.vercel.app/)
 
## 6. Screenshots

![Todof](https://github.com/user-attachments/assets/11792935-7987-40b5-ab24-74015af97065)


![todob](https://github.com/user-attachments/assets/3f1525b0-e63e-4780-9448-2318de9ff36d)



## 7. Who Are You?

Please list your **GitHub** and **Linkedin** handles if you feel comfortable doing so. 

- [Vaishnavi on github](https://github.com/Vaishnavi-Raykar)
- [Vaishnavi on linkedin](https://www.linkedin.com/in/vaishnavi-raykar-554827265/)

## 5 Extra Bonus Points
Link to any bonus activities you did such as solving GitHub Issues or posting to Dev.to, Twitter, etc related to your project and CopilotKit.
- A dev.to blog link - [Link](https://dev.to/vaishnavi_raykar/priority-based-todo-list-4ap6)

#issue 626 : Build a TodoList that helps to add task using CopilotKit's AI interaction capabilities.

## 🚀 **Project Title**
⏰[**AI Powered GitHub Pull Request Tracker**](https://github-ai-pr-tracker.vercel.app/) - An AI-powered tool designed to efficiently track your GitHub pull requests across various open-source events like Hacktoberfest and Devfest.
### 📝 **Name of Issue/Topic**
CopilotKit Demo

---

### 🛠️ **Technologies Being Used**

- **Next.js** - Framework for building server-rendered React applications.
- **React** - JavaScript library for building user interfaces.
- **Tailwind CSS** - Utility-first CSS framework for rapid UI development.
- **Shadcn-UI** - Components for building accessible and customizable UI.
- **Apollo Client** - State management library for GraphQL.
- **CopilotKit API** - For AI chatbot [[Copilot Kit]](https://github.com/copilotkit/copilotkit)

---
### ✨Features 
- **AI Assistance:** Leverage AI for better insights and tracking of the status your pull requests.
- **Comprehensive Filtering:** Easily filter PRs based on events like Hacktoberfest.
- **User-Friendly Interface:** Enjoy a seamless experience built with modern technologies.
- **Multi-Event Support:** Track PRs across different open-source events.
- **Sharable Links:** Easily generate links to highlight your pull requests or share your progress with others, making collaboration seamless.
---
### 🌐 **App Link**
Live App: [Github Pull Request Tracker](https://github-ai-pr-tracker.vercel.app)

---

### 👨‍💻**Repository**
 GitHub Link: [Repository](https://github.com/jeevaramanathan/github-ai-pr-tracker/)

---

### 🎯 **Bonus Points**

1. Dev.to - [AI-Powered GitHub Pull Request Tracker for Your Hacktoberfest (and More!)](https://dev.to/jeevaramanathan/ai-powered-github-pull-request-tracker-for-your-hacktoberfest-and-more-fm7)
2. x.com - [Post on X](https://x.com/jeevaramanathan/status/1843907368578613517) 
---

### 📸 **Screenshot & Demo Video**
![image](https://github.com/user-attachments/assets/ba31ac55-4755-47e7-8051-3ca4784e5ab0)

https://github.com/user-attachments/assets/32090699-a998-49dd-9fca-6cb4b82f6c1b

---

### 🙋‍♂️ **Crafted by**
- [Jeeva Ramanathan - GitHub](https://github.com/jeevaramanathan/)
- [Jeeva Ramanathan - LinkedIn](https://www.linkedin.com/in/jeevaramanathan/)
- [Jeeva Ramanathan - X](https://x.com/jeevaramanathan/)
- [Jeeva Ramanathan - dev.to](https://dev.to/jeevaramanathan/)
- [Jeeva Ramanathan - medium.com](https://medium.com/@jeevaramanathan/)



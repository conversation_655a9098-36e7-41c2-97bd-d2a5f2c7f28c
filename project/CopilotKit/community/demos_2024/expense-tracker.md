## 1. Expense Tracking
[Expense Tracking](https://copilotkit-expense-tracker.vercel.app/) - 
An Expense Tracker with Copilot, Shadcn-UI, Next js and Groq API. This allows user to add, delete, update expense with Copilot kit chat interface and also the input section.

## 2. Name of Issue/Topic

11 - Budget Tracker (Hacktoberfest Demo) #623

## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)

### Note: Please only use the [Shadcn/ui]([https://v0.dev/docs](https://ui.shadcn.com/docs/installation)) for styling components.

- v0 Styling
- Shadcn-UI Component Styling
- CopilotKit
- Nextjs
- Groq API

## 4. GitHub Project Link

### GitHub Repo Link: 
[Github Link](https://github.com/TRIPLE-ADE/copilotkit-expense-tracker)

### 5 bonus points

- If your app is live, include the link here:
[Live Demo](https://copilotkit-expense-tracker.vercel.app/)

## 6. Screenshot

Include a screenshot of your demo in action:
![image](https://github.com/TRIPLE-ADE/copilotkit-expense-tracker/blob/main/public/Screenshot2.png)

## 7. Who Are You?

Please list your **GitHub** and **Twitter** handles if you feel comfortable doing so. 

We want to promote what you've built and your hard work
- [Abdulrasheed on Github](https://github.com/TRIPLE-ADE)
- [Abdulrasheed on Linkedin](https://www.linkedin.com/in/abdulsalam-dev)

## 5 Extra Bonus Points

Link to any bonus activities you did such as solving GitHub Issues or posting to Twitter, Dev.to, Medium, etc related to your project and CopilotKit.

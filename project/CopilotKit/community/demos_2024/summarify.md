## 1. Summarify - Content Summary Tool

## 2. Name of Issue/Topic

⭐ 27 - Content Summary Tool (Hacktoberfest Demo)

## 3. Technologies Being Used

- NextJS
- CopilotKit
- OpenAI
- Tailwind css
- v0 Styling
- Shadcn-UI Component Styling
- CopilotKit

## 4. GitHub Project Link

### GitHub Repo Link: 
https://github.com/jainanuj94/summarify

- Try it out: [Live Demo](https://summarify-puce.vercel.app/)

## 5. Features

- Summarize your content using CopilotKit API.
    - useCopilotReadable and useCopilotChat to get the summarized version of your content.
- Provide suggestions for writing an article to summerize
    - useCopilotAction to handle suggestions coming from LLM and rendering them on the UI.
- Use Extract to extract the contents from the URL.

## 6. Screenshot

Summarify - Content Summary Tool:
![image](https://github.com/user-attachments/assets/44b6adca-bffb-4c60-b056-59760e6fcde1)
Suggestions:
![image](https://github.com/user-attachments/assets/23c6d02c-5dac-4b43-b46e-186928854ca1)

## 7. Who Are You?

[**GitHub Username:**](https://github.com/jainanuj94) 
[**Twitter Handle:**](https://x.com/jainanuj94)
[**LinkedIn Profile:**](https://www.linkedin.com/in/anuj-jain-5300448a)

## 5 Extra Bonus Points

Link to any bonus activities you did such as solving GitHub Issues or posting to Twitter, Dev.to, Medium, etc related to your project and CopilotKit.


# 1. Task Manager

In this project, I developed a Task Manager using React for the frontend, Tailwind CSS for styling, and Shadcn UI for enhanced UI components.
### Method 1 - Using Form
- User can add Task using form with its detail description, date & time and also needs to add priority of task.
- User also show task completed as well as delete the task.
### Method 2 - Using Copilotkit
- User need to write task name,task details also update and delete the task.

## 2. Name of Issue/Topic

⭐ 03 - Task Manager (Hacktoberfest Demo) #604

## 3. Technologies Being Used

List the technologies and frameworks you used (e.g., CopilotKit, Next.js)
- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [CopilotKit](https://copilotkit.ai)
- [ShadCN](https://ui.shadcn.com)
- Shadcn-UI Component Styling
- CopilotKit

## 4. GitHub Project Link

### [Link-](https://github.com/Vaishnavi-<PERSON><PERSON>/task-manager)

## 5. Bonus points

- If your app is live, include the link here:
- [Live app](https://task-manager-nu-cyan.vercel.app/)

## 6. Who Are You?

Please list your **GitHub** and **Linkedin** handles if you feel comfortable doing so. 

- [Vaishnavi on github](https://github.com/Vaishnavi-Raykar)
- [Vaishnavi on linkedin](https://www.linkedin.com/in/vaishnavi-raykar-554827265/)

#issue 604 : Build a Task Manager that helps to add task using CopilotKit's AI interaction capabilities.

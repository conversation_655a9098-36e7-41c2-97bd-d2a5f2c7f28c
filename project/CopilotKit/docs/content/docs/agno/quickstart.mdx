---
title: Quickstart
description: Turn your Agno Agents into an agent-native application in 10 minutes.
icon: "lucide/Play"
---

import {
  TailoredContent,
  TailoredContentOption,
} from "@/components/react/tailored-content.tsx";
import { CoAgentsEnterpriseCTA } from "@/components/react/coagents/coagents-enterprise-cta.tsx";
import { CoAgentsDiagram } from "@/components/react/coagents/coagents-diagram.tsx";
import { FaPython, FaJs, FaCloud } from "react-icons/fa";
import SelfHostingCopilotRuntimeCreateEndpoint from "@/snippets/self-hosting-copilot-runtime-create-endpoint.mdx";
import CopilotCloudConfigureRemoteEndpointLangGraph from "@/snippets/copilot-cloud-configure-remote-endpoint-langgraph.mdx";
import CopilotKitCloudCopilotKitProvider from "@/snippets/copilot-cloud-configure-copilotkit-provider.mdx";
import LangGraphPlatformDeploymentTabs from "@/snippets/langgraph-platform-deployment-tabs.mdx";
import { Accordions, Accordion } from "fumadocs-ui/components/accordion";
import FindYourCopilotRuntime from "@/snippets/find-your-copilot-runtime.mdx";
import CloudCopilotKitProvider from "@/snippets/coagents/cloud-configure-copilotkit-provider.mdx";
import SelfHostingCopilotRuntimeConfigureCopilotKitProvider from "@/snippets/coagents/self-host-configure-copilotkit-provider.mdx";
import SelfHostingCopilotRuntimeLangGraphEndpoint from "@/snippets/self-hosting-copilot-runtime-langgraph-endpoint.mdx";
import SelfHostingCopilotRuntimeStarter from "@/snippets/self-hosting-copilot-runtime-starter.mdx";
import SelfHostingRemoteEndpoints from "@/snippets/self-hosting-remote-endpoints.mdx";
import {
  UserIcon,
  PaintbrushIcon,
  WrenchIcon,
  RepeatIcon,
  ServerIcon,
} from "lucide-react";
import CopilotUI from "@/snippets/copilot-ui.mdx";

<video
  src="/images/coagents/chat-example.mp4"
  className="rounded-lg shadow-xl"
  loop
  playsInline
  controls
  autoPlay
  muted
/>

## Prerequisites

Before you begin, you'll need the following:

- An OpenAI API key
- Node.js 18+
- Your favorite Node package manager
- pip

## Getting started

<Steps>
    <TailoredContent
        className="step"
        id="agent"
        header={
            <div>
                <p className="text-xl font-semibold">Choose your starting point</p>
                <p className="text-base">
                    You can either start fresh with our starter template or integrate CopilotKit into your existing Agno Agent.
                </p>
            </div>
        }
    >
        <TailoredContentOption
            id="coagents-starter"
            title="Start with our template"
            description="Get started quickly with our pre-configured starter template."
            icon={<img src="/images/copilotkit-logo.svg" alt="CopilotKit Logo" width={20} height={20} />}
        >
            <Step>
                ### Clone the starter template

                ```bash
                git clone https://github.com/CopilotKit/with-agno copilotkit-agno-starter
                cd copilotkit-agno-starter
                ```
            </Step>
            <Step>
                ### Install dependencies

                Now we need to install the depedencies for the frontend and the agent. This repo comes with a helper function to do this for you.

                <Callout type="info">
                    This Agno agent uses pip and manages the installation of dependencies through an package.json script, `install:agent`.
                </Callout>

                <Tabs groupId="package-manager" items={['npm', 'pnpm', 'yarn', 'bun']}>
                    <Tab value="npm">
                        ```bash
                        npm install
                        npm run install:agent
                        ```
                    </Tab>
                    <Tab value="pnpm">
                        ```bash
                        pnpm install
                        pnpm install:agent
                        ```
                    </Tab>
                    <Tab value="yarn">
                        ```bash
                        yarn install
                        yarn install:agent
                        ```
                    </Tab>
                    <Tab value="bun">
                        ```bash
                        bun install
                        bun install:agent
                        ```
                    </Tab>
                </Tabs>
            </Step>
            <Step>
                ### Configure your environment

                This Agno agent uses OpenAI's `gpt-4o` by default, so we need to set the `OPENAI_API_KEY` environment variable.

                ```bash
                export OPENAI_API_KEY="your_openai_api_key"
                ```

                <Callout type="info" title="What about other models?">
                  The starter template is configured to use OpenAI's GPT-4o by default, but you can modify it to use any language model supported by Agno.
                </Callout>
            </Step>
            <Step>
                ### Start the development server
                This will start both the frontend and agent servers concurrently.

                <Tabs groupId="package-manager" items={['npm', 'pnpm', 'yarn', 'bun']}>
                    <Tab value="npm">
                        ```bash
                        npm run dev
                        ```
                    </Tab>
                    <Tab value="pnpm">
                        ```bash
                        pnpm dev
                        ```
                    </Tab>
                    <Tab value="yarn">
                        ```bash
                        yarn dev
                        ```
                    </Tab>
                    <Tab value="bun">
                        ```bash
                        bun dev
                        ```
                    </Tab>
                </Tabs>

                This will start both the UI and agent servers concurrently.
            </Step>
        </TailoredContentOption>
        <TailoredContentOption
            id="bring-your-own"
            title="Integrate with existing agent"
            description="I already have a Agno Agent and want to add CopilotKit."
            icon={<img src="/images/copilotkit-logo.svg" alt="CopilotKit Logo" width={20} height={20} />}
        >
            <Step>
                ### Serve your Agno agent as a AG-UI endpoint

                ```python title="agent.py"
                from agno.app.agui.app import AGUIApp
                from agno.agent.agent import Agent
                
                # Setup your Agno Agent, can be any Agno Agent
                agent = Agent(
                    name="Agno Assistant",
                    model=OpenAIChat(id="gpt-4o"),
                    instructions="You are a helpful AI assistant.",
                )
                
                # Setup the AG-UI app
                agui_app = AGUIApp(
                    agent=agent,
                    name="AG-UI Agno Agent",
                    app_id="agno_agent",
                )
                app = agui_app.get_app()
                
                # Serve the app, effectively exposing your Agno Agent
                agui_app.serve(app="agno_agent:app", port=8000, reload=True)
                ```
            </Step>
            <Step>
                ### Frontend Setup
                CopilotKit works with any React-based frontend. If you don't have one yet, you can create a new Next.js app:

                ```bash
                npx create-next-app@latest my-copilot-app
                cd my-copilot-app
                ```

                <Callout type="info" title="What about other frameworks?">
                    While we recommend Next.js for its simplicity and features, CopilotKit will work with any React framework (Vite, Remix, Tanstack, etc.) or custom React setup.
                </Callout>
            </Step>
            <Step>
                ### Install CopilotKit packages

                ```package-install
                @copilotkit/react-ui @copilotkit/react-core @copilotkit/runtime @ag-ui/agno
                ```
            </Step>
            <Step>
                ### Setup Copilot Runtime

                CopilotKit requires a Copilot Runtime endpoint to safely communicate with your agent. This can be served
                anywhere that Node.js can run, but for this example we'll just use Next.js.

                ```ts title="app/api/copilotkit/route.ts"
                import {
                  CopilotRuntime,
                  ExperimentalEmptyAdapter,
                  copilotRuntimeNextJSAppRouterEndpoint,
                } from "@copilotkit/runtime";
                // [!code highlight:3]
                import { AgnoAgent } from "@ag-ui/agno"
                import { NextRequest } from "next/server";
                
                // 1. You can use any service adapter here for multi-agent support. We use
                //    the empty adapter since we're only using one agent.
                const serviceAdapter = new ExperimentalEmptyAdapter();

                // [!code highlight:8]
                // 2. Create the CopilotRuntime instance and utilize the Agno AG-UI
                //    integration to setup the connection.
                const runtime = new CopilotRuntime({
                     agents: {
                        // Our FastAPI endpoint URL
                        "agno_agent": new AgnoAgent({url: "http://localhost:8000/agui"}),
                    }   
                });

                // 3. Build a Next.js API route that handles the CopilotKit runtime requests.
                export const POST = async (req: NextRequest) => {
                  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
                    runtime, // [!code highlight]
                    serviceAdapter,
                    endpoint: "/api/copilotkit",
                  });
                
                  return handleRequest(req);
                };
                ```
            </Step>
            <Step>
                ### Configure CopilotKit Provider

                Next, wrap your application with the CopilotKit provider so that CopilotKit can take control across your application
                via the Agno agent.

                ```tsx title="app/layout.tsx"
                import { CopilotKit } from "@copilotkit/react-core"; // [!code highlight]
                import "@copilotkit/react-ui/styles.css";

                // ...

                export default function RootLayout({ children }: {children: React.ReactNode}) {
                  return (
                    <html lang="en">
                      <body>
                        /* [!code highlight:7] */
                        <CopilotKit 
                          runtimeUrl="/api/copilotkit" 
                          agent="agno_agent"
                        >
                          {children}
                        </CopilotKit>
                      </body>
                    </html>
                  );
                }

                ```
            </Step>
            <Step>
              ### Add the chat interface

              Add the CopilotSidebar component to your page:

              ```tsx title="app/page.tsx"
              import { CopilotSidebar } from "@copilotkit/react-ui"; // [!code highlight]

              export default function Page() {
                return (
                  <main>
                    <h1>Your App</h1>
                    <CopilotSidebar /> // [!code highlight]
                  </main>
                );
              }
              ```
            </Step>
        </TailoredContentOption>
    </TailoredContent>
    <Step>
        ### 🎉 Start chatting!

        Your AI agent is now ready to use! Try asking it some questions:

        ```
        What tools do you have access to?
        ```

        ```
        What do you think about React?
        ```

        ```
        Show me some cool things you can do!
        ```

        <Accordions className="mb-4">
            <Accordion title="Troubleshooting">
                - If you're having connection issues, try using `0.0.0.0` or `127.0.0.1` instead of `localhost`
                - Make sure your Agno agent is running on port 8000
                - Check that your OpenAI API key is correctly set in the `.env` file
            </Accordion>
        </Accordions>
    </Step>
</Steps>

## What's next?

Now that you have your basic agent setup, explore these advanced features:

<Cards>
  <Card
    title="Implement Human in the Loop"
    description="Allow your users and agents to collaborate together on tasks."
    href="/agno/human-in-the-loop"
    icon={<UserIcon />}
  />
  <Card
    title="Add some generative UI"
    description="Render your agent's progress and output in the UI."
    href="/agno/generative-ui"
    icon={<PaintbrushIcon />}
  />
  <Card
    title="Setup frontend actions"
    description="Give your agent the ability to call frontend tools, directly updating your application."
    href="/agno/frontend-actions"
    icon={<WrenchIcon />}
  />
</Cards> 
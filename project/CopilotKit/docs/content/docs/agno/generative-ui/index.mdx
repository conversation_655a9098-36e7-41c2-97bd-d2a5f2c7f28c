---
title: Generative UI
icon: "lucide/Paintbrush"
description: Render your agent's behavior with custom UI components.
---

import { CTACards } from "@/components/react/cta-cards";
import { UserIcon, PaintbrushIcon, WrenchIcon, RepeatIcon } from "lucide-react";

<Frame>
  <img
    src="/images/coagents/AgenticGenerativeUI.gif"
    className="my-0"
    alt="Demo of Generative UI showing a meeting scheduling agent"
  />
</Frame>

<Callout>
  This example shows our [Research Canvas](/agno/videos/research-canvas)
  making use of Generative UI!
</Callout>

## What is Generative UI?

Generative UI lets you render your agent's state, progress, outputs, and tool calls with custom UI components in real-time. It bridges the gap between AI
agents and user interfaces. As your agent processes information and makes decisions, you can render custom UI components that:

- Show loading states and progress indicators
- Display structured data in tables, cards, or charts
- Create interactive elements for user input
- Animate transitions between different states

---

<Cards className="gap-6">
  <Card
    className="p-6 rounded-xl text-base"
    icon={<WrenchIcon className="text-[#FF3C1A]" />}
    title="Tool-based"
    description="Render your agent's tool calls with custom UI components."
    href="/agno/generative-ui/tool-based"
  />
</Cards>

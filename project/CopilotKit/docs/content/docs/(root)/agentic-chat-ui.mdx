---
title: Agentic Chat UI
icon: "lucide/MessageSquare"
description: Create intelligent chat interfaces powered by AI agents that can take actions and maintain context.
---
import { IntegrationsGrid } from "@/components/react/integrations";

## What is Agentic Chat UI?

Agentic Chat UI is an intelligent chat interface that goes beyond simple text exchange. It's powered by AI agents that can take actions, maintain context, access application state, and provide interactive experiences within the chat interface itself.

<video src="/images/coagents/agentic-chat-ui.mp4" className="rounded-lg shadow-xl" loop playsInline controls autoPlay muted />

Unlike traditional chatbots that only respond with text, agentic chat interfaces can execute functions, display rich content, and create collaborative experiences between users and AI agents.

<Callout title="Ready to Implement Agentic Chat UI?">
Agentic Chat UI can be implemented with any Agent Framework, with each framework providing different approaches for creating intelligent, action-oriented chat experiences.

**Choose your integration to see framework-specific implementation guides and examples.** 
<IntegrationsGrid targetPage="agentic-chat-ui" suppressDirectToLLM />
</Callout>

<div className="grid grid-cols-1 md:grid-cols-2 gap-8">

<div>


## Key Features

### **Agent-Powered Responses**
Messages are generated by intelligent agents that understand context and can take appropriate actions.

### **Interactive Message Components**
Chat messages can contain buttons, forms, visualizations, and other interactive elements.

### **Real-time State Updates**
Chat interface updates in real-time as agents process information and modify application state.

### **Action Execution**
Agents can execute both frontend and backend actions directly from chat interactions.

### **Context Persistence**
Conversation context and agent state persist across sessions and interactions.

### **Multi-modal Communication**
Support for text, images, files, and custom interactive components within the chat.

</div>

<div>

## When to Use Agentic Chat UI

Consider Agentic Chat UI when you want to:

- **Provide natural language interfaces** for complex applications
- **Enable task completion** through conversational interactions
- **Create collaborative experiences** between users and AI
- **Simplify complex workflows** with guided conversations
- **Offer contextual help** that understands current application state
- **Build engaging user experiences** that go beyond traditional forms and menus
- **Provide accessible interfaces** that work well for users of all technical levels

</div>

</div>



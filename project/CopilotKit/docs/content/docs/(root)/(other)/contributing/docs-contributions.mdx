---
title: Documentation Contributions
---

We understand that as we move quickly, sometimes our documentation website can be a bit outdated. Therefore, we highly value contributions to our documentation.

## Prerequisites

- [Node.js](https://nodejs.org/en/) 20.x or later
- [pnpm](https://pnpm.io/) v9.x installed globally (`npm i -g pnpm@^9`)

## How To Contribute

<Steps>
  <Step>
  ### Fork The Repository

  First, head over to the [CopilotKit GitHub repository](https://github.com/CopilotKit/CopilotKit) and create a fork.

  Then, clone the forked repository to your local machine:

  ```shell
  git clone https://github.com/<your-username>/CopilotKit
  cd CopilotKit/docs
  ```
  </Step>
  <Step>
  ### Run the Documentation Site Locally

  To run the documentation site locally, install the dependencies and then start the docs in development mode:

  ```shell
  pnpm install
  pnpm run dev
  ```

  The documentation site should be available at [http://localhost:3000](http://localhost:3000).
  </Step>
  <Step>
  ### Make Your Changes

  Now, you can make your changes to the documentation website.

  - All documentation-related files are located in the docs repository
  - You may want to familiarize yourself with [Nextra](https://nextra.site/) to understand how the documentation website is structured.

  <Callout type="info">
    Please ensure you review your changes for grammar, spelling and formatting errors. Also, ensure that links and images are working.
  </Callout>
  </Step>
  <Step>
  ### Review Changes & Submit Pull Request

  Once you are happy with your changes, you can commit and push them. Then, head over to the [Pull Requests page](https://github.com/CopilotKit/CopilotKit/pulls) and create a pull request. Thank you for your contribution!
  </Step>
</Steps>

## Need help?

If you need help with anything, please don't hesitate to reach out to us on [Discord](https://discord.gg/6dffbvGU3D). We have a dedicated [#contributing](https://discord.com/channels/1122926057641742418/1183863183149117561) channel.

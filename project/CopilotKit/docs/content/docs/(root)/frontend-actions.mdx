---
title: Frontend Actions
icon: "lucide/Wrench"
description: Create frontend actions and use them within your agent.
---
import { IntegrationsGrid } from "@/components/react/integrations";

<video src="/images/frontend-actions-demo.mp4" className="rounded-lg shadow-xl" loop playsInline controls autoPlay muted />
<Callout>
    This video shows the [coagents starter](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-starter) repo with the [implementation](#implementation) section applied to it!
</Callout>

## What is this?

Frontend actions are powerful tools that allow your AI agents to directly interact with and update your application's user interface. Think of them as bridges that connect your agent's decision-making capabilities with your frontend's interactive elements.

## When should I use this?

Frontend actions are essential when you want to create truly interactive AI applications where your agent needs to:

- Dynamically update UI elements
- Trigger frontend animations or transitions
- Show alerts or notifications
- Modify application state
- Handle user interactions programmatically

Without frontend actions, agents are limited to just processing and returning data. By implementing frontend actions, you can create rich, interactive experiences where your agent actively drives the user interface.

<Callout title="Ready to Implement Frontend Actions?">
Frontend actions can be implemented with any Agent Framework, with each framework providing different approaches for connecting agents to your UI.

**Choose your integration to see framework-specific implementation guides and examples.** 
<IntegrationsGrid targetPage="frontend-actions" />
</Callout> 
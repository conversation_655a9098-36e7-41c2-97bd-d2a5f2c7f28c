---
title: What is CopilotKit?
description: "The Agentic Application Framework:  Open source framework and hosted service for AI-assisted applications."
icon: "lucide/Sparkles"
---
import { ImageZoom } from 'fumadocs-ui/components/image-zoom';
import { ExamplesCarousel } from "@/components/react/examples-carousel";
import { CTACards } from "@/components/react/cta-cards";
import { YouTubeVideo } from "@/components/react/youtube-video";
import { MdMessage } from "react-icons/md";
import { TbSparkles } from "react-icons/tb";
import { SiLangchain } from "react-icons/si";
import { AG2Icon } from "@/lib/icons/custom-icons";
import { Si<PERSON>rewai } from "@icons-pack/react-simple-icons";
import { FileSpreadsheet, Banknote, Plane, BookOpen, Telescope, Play } from "lucide-react";
import { Accordions } from 'fumadocs-ui/components/accordion';
import { Accordion } from 'fumadocs-ui/components/accordion';
import { LinkToCopilotCloud } from "@/components/react/link-to-copilot-cloud";
import { QuickstartDropdown } from "@/components/react/quickstart-dropdown";
import { IntegrationsGrid } from "@/components/react/integrations";
import { MaximizableVideo } from "@/components/react/maximizable-video";

<div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start -mt-6">
  <div className="[&>p]:mb-4">
    <div>
      CopilotKit is the easiest way to add AI copilots - intelligent, context-aware assistants - into your app.

      CopilotKit connects your app's **logic, state, and user context** to the **AI agents** that deliver the animated and interactive part of your app experience — across both embedded UIs and fully headless interfaces. It gives you the tools to build, deploy, and monitor AI-assisted features that feel intuitive, helpful, and deeply integrated.

      By decoupling your application from any specific model or agent architecture, CopilotKit gives you the freedom to evolve your AI stack without rethinking your user experience.
    </div>
  </div>
  <div className="flex flex-col items-center">
    <MaximizableVideo 
      src="/images/coagents/tutorials/ai-travel-app/demo.mp4"
      className="w-full max-w-md"
    />
    <div className="w-full max-w-md mt-6">
        <Callout title="Want to build your own travel app?">
        Check out our [Travel App Tutorial](/coagents/tutorials/ai-travel-app) to see how to build your own travel app with CopilotKit.
        </Callout>
    </div>
  </div>
</div>

## Get Started Now
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-10 gap-6 mb-12">
  <div className="lg:col-span-3 border-2 border-border px-6 pt-1 pb-4 rounded-xl text-center transition-colors min-h-[180px] flex flex-col justify-between">
    <div>
      <h3 className="text-xl font-bold mb-3 text-foreground">Quickstart</h3>
      <p className="text-sm text-muted-foreground mb-8">Start with an LLM<br /> or Agent Framework</p>
    </div>
    <div className="h-12 flex items-center justify-center">
      <QuickstartDropdown />
    </div>
  </div>
  <div className="lg:col-span-4 border-2 border-border px-6 pt-1 pb-4 rounded-xl text-center transition-colors min-h-[180px] flex flex-col justify-between">
    <div>
      <h3 className="text-xl font-bold mb-3 text-foreground">CLI</h3>
      <p className="text-sm text-muted-foreground mb-4">Bootstrap your NextJS<br />application with CopilotKit</p>
    </div>
    <div className="h-12 flex items-center justify-center">
      
```bash
npx copilotkit@latest init
```
    </div>
  </div>
  <div className="lg:col-span-3 border-2 border-border px-6 pt-1 pb-4 rounded-xl text-center transition-colors min-h-[180px] flex flex-col justify-between">
    <div>
      <h3 className="text-xl font-bold mb-3 text-foreground">Platform</h3>
      <p className="text-sm text-muted-foreground mb-4">Start the easiest way<br /> with our hosted service</p>
    </div>
    <div className="h-12 flex items-center justify-center">
      <LinkToCopilotCloud asButton={true} />
    </div>
  </div>
  
</div>

## Integrations

CopilotKit works with any LLM or agent framework.  Choose an integration to see the full feature set.

<IntegrationsGrid />

## Key Features
Learn why CopilotKit is used by thousands of developers around the world.

<div className="flex flex-row gap-8 my-8">
  <div className="w-1/2">
    <Accordions>
      <Accordion title="Designed for Agentic Applications">
        Unlike general-purpose frameworks, CopilotKit is designed from the ground up to support agent-driven user experiences.
      </Accordion>
      <Accordion title="Context Injection">
        Automatically sync app state, user input, and environmental data to give agents meaningful grounding.
      </Accordion>
      <Accordion title="AGUI Protocol">
        Maintain real-time synchronization between agent actions and user interfaces.
      </Accordion>
      <Accordion title="MCP Support">
        Integrate with MCP servers to support secure, multi-agent coordination and external orchestration.
      </Accordion>
      <Accordion title="UI Integration">
        Drop-in React components for chat UIs, inline assistants, and guided workflows.
      </Accordion>
      <Accordion title="Headless Mode">
        Use CopilotKit logic and messaging infrastructure without a proscribed visual interface—full programmatic control.
      </Accordion>
      <Accordion title="LLM & Agent Framework Agnostic">
        Unlocks the power of LangGraph, CrewAI, Autogen2, and more. Swap out backends with no UI rewrite.
      </Accordion>
      <Accordion title="Hosted or Self-Hosted">
        Use the hosted cloud service for simplicity, or run the open source stack in your environment.
      </Accordion>
    </Accordions>
  </div>
  <div className="w-1/2">
    <Accordions>
      <Accordion title="Full Flexibility">
        Bring your own LLMs, tools, and agents — CopilotKit doesn't lock you into a specific stack.
      </Accordion>
      <Accordion title="Real-Time Interaction">
        Syncs seamlessly between user inputs, UI state, and AI agent responses.
      </Accordion>
      <Accordion title="Unified DevX">
        Declarative APIs and React-native paradigms that feel familiar and productive.
      </Accordion>
      <Accordion title="Production-Ready">
        Built-in observability, debugging, and extensibility to support real-world deployment and scaling.
      </Accordion>
      <Accordion title="Cloud or OSS">
        Use our hosted platform or run it entirely within your own infrastructure.
      </Accordion>
      <Accordion title="Community & Extensibility">
        Open source with growing integrations, patterns, and examples.
      </Accordion>
      <Accordion title="Observability & Debugging">
        Built-in tools to trace agent decisions, model responses, and message flows.
      </Accordion>
      <Accordion title="Composable Architecture">
        Mix and match UI, logic, and AI components like building blocks.
      </Accordion>
    </Accordions>
  </div>
</div>

 


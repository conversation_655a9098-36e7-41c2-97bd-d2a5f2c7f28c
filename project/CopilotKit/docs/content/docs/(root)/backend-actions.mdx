---
title: Backend Actions
icon: "lucide/Server"
description: Enable AI agents to execute server-side operations, access databases, and integrate with external services.
---
import { RocketIcon } from "lucide-react";

## What are Backend Actions?

Backend Actions are server-side functions that AI agents can call to perform operations that require backend resources, such as database queries, API calls, file operations, or complex computations. They extend agent capabilities beyond the frontend to access the full power of your server infrastructure.

Unlike frontend actions that modify UI state, backend actions handle data processing, external integrations, and operations that require server-side security or resources.

<Callout>
**Ready to get started with Backend Actions?**

  <div className="flex items-center gap-6">
    <div className="flex-shrink-0">
      <a 
        href="/direct-to-llm/guides/backend-actions"
        className="flex flex-col items-center gap-1 text-center no-underline group"
      >
        <div className="w-12 h-12 flex items-center justify-center rounded-2xl transition-all duration-200 group-hover:scale-105 bg-gradient-to-b from-green-700 to-green-400 text-green-100">
          <RocketIcon className="w-8 h-8" />
        </div>
        <span className="text-sm font-medium text-zinc-700 dark:text-zinc-300 group-hover:text-black dark:group-hover:text-white transition-colors duration-200">
          Direct to LLM
        </span>
      </a>
    </div>
    <div className="flex-1">
      <p className="text-base leading-relaxed mb-4">
        Backend Actions are primarily designed for Direct to LLM applications, where you need explicit server-side functions that the LLM can call. CopilotKit provides multiple implementation approaches.
      </p>
    </div>
  </div>
</Callout>

<div className="grid grid-cols-1 md:grid-cols-2 gap-8">

<div>

## Common Backend Action Patterns

### **Database Operations**
Actions that create, read, update, or delete data from databases, often with complex queries and relationships.

### **API Integrations**
Actions that call external APIs, process responses, and integrate data from third-party services.

### **File Operations**
Actions that handle file uploads, downloads, processing, or storage operations on the server.

### **Authentication and Authorization**
Actions that manage user authentication, permissions, and access control for sensitive operations.

### **Data Processing**
Actions that perform data transformation, analysis, aggregation, or complex computations.

### **Workflow Orchestration**
Actions that coordinate multiple backend services or trigger complex business processes.

</div>

<div>

## Types of Backend Actions

### **Synchronous Actions**
Actions that execute and return results immediately, suitable for quick database queries or simple API calls.

### **Asynchronous Actions**
Actions that handle long-running operations, often with progress tracking and completion notifications.

### **Streaming Actions**
Actions that return data progressively, useful for real-time updates or large data sets.

### **Batch Actions**
Actions that process multiple items or operations together for efficiency.

### **Transactional Actions**
Actions that ensure data consistency across multiple operations, with rollback capabilities on failure.

</div>

</div>

## When to Use Backend Actions

Consider Backend Actions when agents need to:

- **Access sensitive data** that shouldn't be exposed to the frontend
- **Perform complex queries** across multiple data sources
- **Integrate with external services** requiring server-side authentication
- **Execute business logic** that involves multiple systems
- **Process large amounts of data** efficiently
- **Maintain data consistency** across operations
- **Handle file operations** or media processing
- **Implement security controls** and access management

### **Agent Framework Applications**

Agent frameworks (LangGraph, CrewAI, LlamaIndex, etc.) typically handle backend operations through their native agent architectures rather than explicit backend actions. These frameworks provide their own mechanisms for:

- Tool calling and external service integration
- Database access and data manipulation  
- Complex workflow orchestration
- Multi-step reasoning and planning

For agent-based applications, backend functionality is usually implemented as **agent tools** or **framework-specific integrations** rather than CopilotKit backend actions. 
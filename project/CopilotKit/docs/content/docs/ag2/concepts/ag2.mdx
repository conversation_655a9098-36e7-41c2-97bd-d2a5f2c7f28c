---
title: AG2
description: An agentic framework for building LLM applications that can be used with Copilotkit.
icon: custom/ag2
---

<Frame>
  <img
    src="/images/coagents/ag2/AG2-CopilotKit.png"
    alt="AG2 CopilotKit"
    className="mb-10"
  />
</Frame>

AG2 is an agentic framework for building LLM applications that can be used with CopilotKit. This integration allows developers to combine and coordinate AI tasks efficiently, providing a robust framework for building sophisticated AI applications.

## AG2 as CoAgents

CopilotKit now integrates with AG2, bringing together AG2's multi-agent orchestration capabilities with CopilotKit's React UI components. This integration creates a more seamless development experience for building AI-powered applications.
At its core, CopilotKit is a set of tools that make it easy to let your users work alongside Large Language Models (LLMs) to accomplish generative tasks directly in your application. Instead of just using the LLM to generate content, you can let it take direct action alongside your users.
Key features of the AG2 and CopilotKit integration include:

- **Multi-Agent Orchestration**: AG2 enables sophisticated agent workflows and orchestration patterns
- **Human-in-the-Loop**: Human in the Loop (HITL) is a powerful pattern that enables your AG2 agents to collaborate with humans during their workflow. Instead of making all decisions independently, agents can check with human operators at critical decision points, combining AI efficiency with human judgment.

For a detailed guide on setting up AG2 CoAgents with CopilotKit, check out our [quickstart guide](/ag2/quickstart/ag2). 

## Learn More

For more information about AG2, check out the [AG2 documentation](https://docs.ag2.ai).

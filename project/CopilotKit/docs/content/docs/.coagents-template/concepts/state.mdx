---
title: Shared State
description: CoAgents maintain a shared state across your UI and agent execution.
---

<Frame className="mb-10">
  <img
    src="/images/coagents/coagents-state-diagram.png"
    alt="Agentic Copilot State Diagram"
  />
</Frame>

CoAgents maintain a shared state that seamlessly connects your UI with the agent's execution. This shared state system allows you to:

- Display the agent's current progress and intermediate results
- Update the agent's state through UI interactions
- React to state changes in real-time across your application

The foundation of this system is built on --YOUR-FRAMEWORK--'s stateful architecture. --YOUR-FRAMEWORK-- Agents maintain their
internal state throughout execution, which you can access via the `useCoAgentState` hook.

### Understanding Predicted State

While your agent runs, you can emit state updates using CopilotKit's `emit_intermediate_state` function, ensuring your UI stays synchronized
with the agent's progress. The emitted state is called the **predicted state** and is used to provide immediate feedback about ongoing
operations.

While the core shared state reflects the agent's current function in the flow, the predicted state provides immediate
feedback about ongoing operations. Accordingly, this creates a more fluid user experience by showing real-time progress before the agent
completes its current task.

When the state is updated (when a function finishes executing), the predicted state is updated with the new state.

For example, when your agent is processing a request, the predicted state might show a loading indicator or partial results, while the actual
shared state updates once the operation is complete.

Want help implementing this into your CoAgent application? Check out our [intermediate state streaming](/--YOUR-FRAMEWORK--/shared-state/predictive-state-updates)
documentation. 
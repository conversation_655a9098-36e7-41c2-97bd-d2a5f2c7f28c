---
title: Agentic Copilots
description: Agentic copilots provide you with advanced control and orchestration over your agents.
icon: lucide/Bot
---

import {
  TailoredContent,
  TailoredContentOption,
} from "@/components/react/tailored-content";
import { BsFillCloudHaze2Fill as CloudIcon } from "react-icons/bs";
import { FaServer as SelfHostIcon } from "react-icons/fa6";
import { SiLangchain } from "react-icons/si";
import { LinkIcon } from "lucide-react";
import {
  RocketIcon,
  GraduationCapIcon,
  CodeIcon,
  VideoIcon,
} from "lucide-react";

Before we dive into what agentic copilots are, help us help you by telling us your level of experience with --YOUR-FRAMEWORK--. We'll explain things in a way that best suits your experience level.

<TailoredContent id="experience" defaultOptionIndex={0}>
    <TailoredContentOption 
        id="new"
        title="I'm new to --YOUR-FRAMEWORK--" 
        description="Help me understand what agentic copilots are, where --YOUR-FRAMEWORK-- fits in, and how to get started." 
        icon={<img src="/images/copilotkit-logo.svg" width={7} height={7} />}
    >
        <Frame>
            <img src="/images/coagents/SharedStateCoAgents.gif" alt="CoAgents Shared State" className="mt-0 mb-12"/>
        </Frame>

        ### What are Agents?
        AI agents are intelligent systems that interact with their environment to achieve specific goals. Think of them as 'virtual colleagues' that can handle tasks ranging from
        simple queries like "find the cheapest flight to Paris" to complex challenges like "design a new product layout."

        As these AI-driven experiences (or 'Agentic Experiences') become more sophisticated, developers need finer control over how agents make decisions. This is where specialized
        frameworks like --YOUR-FRAMEWORK-- become essential.

        ### What is --YOUR-FRAMEWORK--?
        --YOUR-FRAMEWORK-- is a framework that gives you precise control over AI agents. --YOUR-FRAMEWORK-- agents allow developers to combine and coordinate coding tasks efficiently,
        providing a robust framework for building sophisticated AI automations.

        ### What are Agentic Copilots?
        Agentic copilots are how CopilotKit brings --YOUR-FRAMEWORK-- agents into your application. If you're familiar with CopilotKit, you know that copilots are AI assistants that
        understand your app's context and can take actions within it. While CopilotKit's standard copilots use a simplified [ReAct pattern](https://www.perplexity.ai/search/what-s-a-react-agent-5hu7ZOaKSAuY7YdFjQLCNQ)
        for quick implementation, Agentic copilots give you --YOUR-FRAMEWORK--'s full orchestration capabilities when you need more control over your agent's behavior.

        ### What are CoAgents?
        CoAgents are what we call CopilotKit's approach to building agentic experiences! They're interchangeable with agentic copilots being a more descriptive term for the overall concept.

        ### When should I use CopilotKit's CoAgents?
        You should use CoAgents when you require tight control over the Agentic runloop, as facilitated by an Agentic Orchestration framework like [--YOUR-FRAMEWORK--](https://--YOUR-FRAMEWORK--.com/).
        With CoAgents, you can carry all of your existing CopilotKit-enabled Copilot capabilities into a customized agentic runloop.

        We suggest beginning with a basic Copilot and gradually transitioning specific components to CoAgents.

        The need for CoAgents spans a broad spectrum across different applications. At one end, their advanced capabilities might not be required at all, or only for a minimal 10% of the application's
        functionality. Progressing further, there are scenarios where they become increasingly vital, managing 60-70% of operations. Ultimately, in some cases, CoAgents are indispensable, orchestrating
        up to 100% of the Copilot's tasks (see [agent-lock mode](/--YOUR-FRAMEWORK--/multi-agent-flows) for the 100% case).

        ### Examples
        An excellent example of the type of experiences you can accomplish with CoAgents applications can be found in our [Research Canvas](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-research-canvas).

        More specifically, it demonstrates how CoAgents allow for AI driven experiences with:
        - Precise state management across agent interactions
        - Sophisticated multi-step reasoning capabilities
        - Seamless orchestration of multiple AI tools
        - Interactive human-AI collaboration features
        - Real-time state updates and progress streaming

        ## Next Steps

        Want to get started? You have some options!

        <Cards>
            <Card
                title="Build your first CoAgent"
                description="Follow a step-by-step tutorial to build a travel app supercharged with CoAgents."
                href="/--YOUR-FRAMEWORK--/quickstart/your-framework"
                icon={<RocketIcon />}
            />
            <Card
                title="Learn more CoAgent concepts"
                description="Learn more about the concepts used to talk about CoAgents and how to use them."
                href="/--YOUR-FRAMEWORK--/concepts/terminology"
                icon={<GraduationCapIcon />}
            />
            <Card
                title="Read the reference documentation"
                description="Just here for some reference? Checkout the reference documentation for more details."
                href="/--YOUR-FRAMEWORK--/reference"
                icon={<CodeIcon />}
            />
            <Card
                title="See examples of CoAgents in action"
                description="Checkout our video examples of CoAgents in action."
                href="/--YOUR-FRAMEWORK--/videos/research-canvas"
                icon={<VideoIcon />}
            />
        </Cards>
    </TailoredContentOption>
    <TailoredContentOption
        id="intermediate"
        title="I'm already using --YOUR-FRAMEWORK--"
        description="Help me understand what agentic copilots are, what Copilotkit does to integrate with --YOUR-FRAMEWORK--, and how to get started."
        icon={<SiLangchain />}
    >

        <Frame className="mt-0 mb-12">
            <img
                src="/images/CoAgents.gif"
                alt="CoAgents demonstration"
                className="w-auto"
            />
        </Frame>

        --YOUR-FRAMEWORK-- is a framework for building deeply customizable AI agents.

        CopilotKit's Agentic Copilots is infrastructure for in-app agent-user interaction, i.e. for transforming agents from autonomous processes to user-interactive 'virtual colleagues' that live inside applications.

        Any --YOUR-FRAMEWORK---based agent can be transformed into an Agentic Copilot with a minimal amount
        of effort to get industry leading agentic UX such as:
        - Shared state between the agent and the application.
        - Intermediate result and state progress streaming
        - Human-in-the-loop collaboration
        - Agentic generative UI
        - And more!

        All of these features are essential to delight instead of frustrate your users with AI features.

        ### What are CoAgents?
        CoAgents are what we call CopilotKit's approach to building agentic experiences! They're interchangeable with agentic copilots being a more descriptive term for the overall concept.

        ## Next Steps
        Want to get started? You have some options!

        <Cards>
            <Card
                title="Quickstart"
                description="Integrate your --YOUR-FRAMEWORK-- agent with CopilotKit in a few minutes."
                href="/--YOUR-FRAMEWORK--/quickstart/your-framework"
                icon={<RocketIcon />}
            />
            <Card
                title="Tutorial: AI Travel App"
                description="Follow a step-by-step tutorial to build a travel app supercharged with CoAgents."
                href="/--YOUR-FRAMEWORK--/tutorials/ai-travel-app/overview"
                icon={<GraduationCapIcon />}
            />
            <Card
                title="Reference"
                description="Learn more about the terms used to talk about CoAgents and how to use them."
                href="/reference"
                icon={<CodeIcon />}
            />
            <Card
                title="Examples"
                description="Checkout our video examples of CoAgents in action."
                href="/--YOUR-FRAMEWORK--/videos/research-canvas"
                icon={<VideoIcon />}
            />
        </Cards>
    </TailoredContentOption>

</TailoredContent> 
---
title: Introduction
icon: "lucide/Sparkles"
description: Build Agent-Native Applications (ANAs) powered by CopilotKit and --YOUR-FRAMEWORK-- Agents.
---

import { BiSolidMessage as TextIcon } from "react-icons/bi";
import { Vsc<PERSON>son as JsonIcon } from "react-icons/vsc";
import { FaDiscord } from "react-icons/fa";
import Link from "next/link";
import { YouTubeVideo } from "@/components/react/youtube-video";
import { CoAgentsEnterpriseCTA } from "@/components/react/coagents/coagents-enterprise-cta.tsx";
import {
  CoAgentsFeatureToggle,
  CoAgentsFeatureRender,
} from "@/components/react/coagents/coagents-features.tsx";
import { DynamicContentWrapper } from "@/components/react/dynamic-content-wrapper";
import { ExamplesCarousel } from "@/components/react/examples-carousel";
import {
  LuPlane,
  LuBookOpen,
  LuLightbulb,
  LuBrainCog,
  <PERSON><PERSON><PERSON>,
} from "react-icons/lu";
import { CoAgentsExamples } from "@/components/react/examples-carousel";
import { CTACards } from "@/components/react/cta-cards";
import { Socials } from "@/components/react/socials";

# Copilot Infrastructure for --YOUR-FRAMEWORK-- Agents

Full user-interaction infrastructure for your agents, to turn your agents into Copilot Agents (CoAgents).

{/* TODO: Add a GIF of this animation for --YOUR-FRAMEWORK-- Agents */}

<Frame className="mt-0 mb-6">
  <video
    src="/images/coagents/your-framework/flows/interaction-layer.mp4"
    alt="CoAgents demonstration"
    className="rounded-lg shadow-xl"
    playsInline
    autoPlay
    muted
    loop
  />
</Frame>

## Building blocks of a CoAgent

Everything you need to build Agent-Native Applications (ANAs), right out of the box.

<CTACards
  columns={2}
  cards={[
    {
      iconKey: "message-square",
      title: "Agentic Chat UI",
      description: "In-app chat powered by your agent.",
      href: "/--YOUR-FRAMEWORK--/agentic-chat-ui",
    },
    {
      iconKey: "share2",
      title: "Shared State",
      description: "Your agent can see everything in your app, and vice versa.",
      href: "/--YOUR-FRAMEWORK--/shared-state",
    },
    {
      iconKey: "layout-template",
      title: "Generative UI",
      description: "UI that updates in real-time based on your agent's state.",
      href: "/--YOUR-FRAMEWORK--/generative-ui",
    },
    {
      iconKey: "wand",
      title: "Frontend Tools",
      description:
        "Give your agent the ability to take action in your application.",
      href: "/--YOUR-FRAMEWORK--/frontend-actions",
    },
    {
      iconKey: "wand",
      title: "Multi-Agent Coordination",
      description:
        "Route your agent to the right agent based on the user's request.",
      href: "/--YOUR-FRAMEWORK--/multi-agent-flows",
    },
    {
      iconKey: "user-cog",
      title: "Human-in-the-Loop",
      description: "Set smart checkpoints where humans can guide your agents.",
      href: "/--YOUR-FRAMEWORK--/human-in-the-loop",
    },
  ]}
/>

{/* TODO: Add CoAgents in action section for --YOUR-FRAMEWORK-- Agents */}

## Ready to get started?

Get started with CoAgents in as little as 5 minutes with one of our guides or tutorials.

<CTACards
  columns={1}
  cards={[
    {
      iconKey: "play",
      title: "Quickstart",
      description: "Learn how to build your first CoAgent in 10 minutes.",
      href: "/--YOUR-FRAMEWORK--/quickstart/your-framework",
    },
  ]}
/>
{/* TODO: Add example tutorials for --YOUR-FRAMEWORK-- Agents */}

## Common Questions

Have a question about CoAgents? You're in the right place!

<Accordions>
<Accordion title="Can you explain what a CoAgent is in more detail?">
Sure! CoAgents are what we call "agentic copilots". Well, what's an agentic copilot then?

Think of a Copilot as a simple and fully LLM controlled assistant that has relatively limited capabilities. An Agentic Copilot then is a copilot
that has been enhanced with the ability to use --YOUR-FRAMEWORK-- agents to perform more complex tasks. This is an extremely powerful way to build AI
powered applications because it gives you, the developer, the ability to control the agent's behavior in a deterministic way while still letting
the agent do its magic.

For more on this topic, checkout our [agentic copilot](/--YOUR-FRAMEWORK--/concepts/agentic-copilots) concept page.

</Accordion>
<Accordion title="Can I attach to an existing thread?">
Development of CoAgents is ongoing. One part of this is the ability to attach to an existing thread which is a high priority on our roadmap.

Stay tuned for updates!

</Accordion>
</Accordions> 
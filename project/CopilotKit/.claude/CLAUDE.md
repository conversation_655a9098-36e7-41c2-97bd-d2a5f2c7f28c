# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Management
- `pnpm install` - Install dependencies
- `pnpm -w clean` - Clean all packages
- `pnpm i && pnpm -w build` - Fresh install and build

### Build System
- `turbo build` - Build all packages
- `turbo dev` - Start development mode for all packages
- `turbo dev --filter="@copilotkit/package-name"` - Start specific package in dev mode
- `turbo clean` - Clean build artifacts
- `pnpm freshbuild` - Clean, install, and build everything

### Testing and Quality
- `turbo test` - Run tests across all packages
- `turbo lint` - Run linting
- `turbo check-types` - Type checking
- `prettier --write "**/*.{ts,tsx,md}"` - Format code
- `prettier --check "**/*.{ts,tsx,md}"` - Check formatting

### Documentation
- `pnpm run docs` - Generate documentation using TypeDoc
- `pnpm run precommit` - Run documentation generation (pre-commit hook)

## Architecture Overview

CopilotKit is a full-stack framework for building AI-powered applications with deeply-integrated AI assistants and agents. The architecture follows a modular design with clear separation between frontend React components, backend runtime services, and agent frameworks.

### Core Architecture Flow
1. **Frontend (React)** - UI components and hooks for user interaction
2. **Runtime Client** - GraphQL client communicating with backend
3. **Runtime Server** - Backend service handling LLM integrations and agent orchestration
4. **Agent Layer** - Optional agents built with LangGraph, CrewAI, or other frameworks
5. **LLM Services** - Integration with various LLM providers (OpenAI, Anthropic, etc.)

### Key Packages

#### @copilotkit/react-core
- Main provider component (`CopilotKit`) that wraps your app
- Core hooks: `useCopilotAction`, `useCopilotChat`, `useCoAgent`, `useCopilotReadable`
- Frontend actions and shared state management with agents
- Location: `/packages/react-core/`

#### @copilotkit/react-ui
- Pre-built UI components: `CopilotSidebar`, `CopilotPopup`, `CopilotChat`
- Custom message renderers and styling (Tailwind CSS + Radix UI)
- Location: `/packages/react-ui/`

#### @copilotkit/react-textarea
- Intelligent textarea with AI autocompletion using SlateJS
- Real-time AI suggestions and contextual assistance
- Location: `/packages/react-textarea/`

#### @copilotkit/runtime
- Backend GraphQL API server (GraphQL Yoga)
- Multiple LLM provider adapters (OpenAI, Anthropic, Google, etc.)
- Agent orchestration, streaming responses, framework integrations
- Location: `/packages/runtime/`

#### @copilotkit/runtime-client-gql
- GraphQL client for frontend-backend communication using URQL
- Real-time streaming and subscriptions, type-safe operations
- Location: `/packages/runtime-client-gql/`

#### @copilotkit/sdk-js
- SDK for LangChain/LangGraph agent integration
- State management between agents and frontend
- Location: `/packages/sdk-js/`

#### @copilotkit/shared
- Common type definitions, utilities, and constants
- Telemetry, analytics, and JSON schema generation utilities
- Location: `/packages/shared/`

### Key Patterns

#### Frontend Actions Pattern
```typescript
useCopilotAction({
  name: "updateData",
  parameters: [{ name: "data", type: "object", required: true }],
  handler: ({ data }) => {
    // Update application state
  },
  render: ({ status, args }) => {
    // Optional UI rendering during execution
  }
});
```

#### Shared State Pattern
```typescript
const { state, setState } = useCoAgent({
  name: "agent_name",
  initialState: { /* initial state */ }
});
```

#### Frontend RAG Pattern
```typescript
useCopilotReadable({
  description: "Current user data",
  value: userData
});
```

### Important Architectural Patterns
- **Agent-Native Design**: Built for AI agents with LangGraph/CrewAI support
- **Generative UI**: Actions can render React components during execution
- **Streaming Architecture**: Real-time streaming of AI responses
- **Modular Service Adapters**: Pluggable architecture for different LLM providers
- **Type Safety**: End-to-end TypeScript with GraphQL schema generation

### Project Structure
- `/CopilotKit/` - Main monorepo with packages, examples, and utilities
- `/examples/` - Demo applications and usage examples
- `/docs/` - Documentation website (Next.js)
- `/sdk-python/` - Python SDK for backend integration
- `/infra/` - Infrastructure and deployment configurations
- `/community/` - Community contributions and demos

### Key Entry Points
- Frontend: `/packages/react-core/src/components/copilot-provider/copilotkit.tsx`
- Backend: `/packages/runtime/src/lib/integrations/` and `/packages/runtime/src/service-adapters/`
- GraphQL: `/packages/runtime/src/graphql/`

## Development Workflow

### Prerequisites
- Node.js 20.x or later
- pnpm v9.x (`npm i -g pnpm@^9`)
- Turborepo v2.x (`npm i -g turbo@2`)

### Setup
1. `pnpm install` - Install dependencies
2. `cd CopilotKit && turbo build` - Build all packages
3. `turbo dev` - Start development mode

### Testing
- Single package: `turbo test --filter="@copilotkit/package-name"`
- All packages: `turbo test`

### Commit Format
```
<type>(<package>): <subject>
```
Types: feat, fix, docs, style, refactor, perf, test, chore